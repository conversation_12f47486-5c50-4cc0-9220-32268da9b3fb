server {
    listen 80;
    server_name localhost;

    # Health check endpoint
    location /health {
        root /var/www/html;
        index health;
        access_log off;
    }

    # Proxy to the C service
    location / {
        proxy_pass http://localhost:3738;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeout settings
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Health check for upstream
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }

    # Additional health check that verifies the C service is responding
    location /service-health {
        proxy_pass http://localhost:3738/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        access_log off;
        
        # Return 200 if service responds, 503 if not
        error_page 502 503 504 = @service_down;
    }

    location @service_down {
        return 503 "Service Unavailable";
        add_header Content-Type text/plain;
    }
}
