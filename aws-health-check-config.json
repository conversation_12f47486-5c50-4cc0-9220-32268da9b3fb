{"healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost/health || exit 1"], "interval": 30, "timeout": 10, "retries": 3, "startPeriod": 60}, "portMappings": [{"containerPort": 80, "hostPort": 80, "protocol": "tcp"}, {"containerPort": 3738, "hostPort": 3738, "protocol": "tcp"}], "loadBalancer": {"targetGroup": {"healthCheckPath": "/health", "healthCheckProtocol": "HTTP", "healthCheckPort": "80", "healthCheckIntervalSeconds": 30, "healthCheckTimeoutSeconds": 10, "healthyThresholdCount": 2, "unhealthyThresholdCount": 3, "matcher": {"httpCode": "200"}}}, "services": {"DispatchACK": {"serviceName": "gkv-dispatch-ack", "containerPort": 3738, "healthCheckPort": 80, "healthCheckPath": "/health"}, "DispatchGKV": {"serviceName": "gkv-dispatch-gkv", "containerPort": 3739, "healthCheckPort": 80, "healthCheckPath": "/health"}, "DispatchMO": {"serviceName": "gkv-dispatch-mo", "containerPort": 3740, "healthCheckPort": 80, "healthCheckPath": "/health"}, "ListenGKV": {"serviceName": "gkv-listen-gkv", "containerPort": 3741, "healthCheckPort": 80, "healthCheckPath": "/health"}, "Maintenance": {"serviceName": "gkv-maintenance", "containerPort": 3742, "healthCheckPort": 80, "healthCheckPath": "/health"}}}