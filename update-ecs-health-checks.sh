#!/bin/bash

# Script para actualizar las task definitions de ECS con health checks

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Verificar que AWS CLI esté instalado
if ! command -v aws &> /dev/null; then
    error "AWS CLI no está instalado"
    exit 1
fi

# Verificar que jq esté instalado
if ! command -v jq &> /dev/null; then
    error "jq no está instalado"
    exit 1
fi

# Definir servicios
declare -A SERVICES=(
    ["dispatch-ack"]="gkv-dispatch-ack"
    ["dispatch-gkv"]="gkv-dispatch-gkv"
    ["dispatch-mo"]="gkv-dispatch-mo"
    ["listen-gkv"]="gkv-listen-gkv"
    ["maintenance"]="gkv-maintenance"
)

# Función para crear health check configuration
create_health_check_config() {
    cat << 'EOF'
{
  "command": [
    "CMD-SHELL",
    "curl -f http://localhost/health || exit 1"
  ],
  "interval": 30,
  "timeout": 10,
  "retries": 3,
  "startPeriod": 60
}
EOF
}

# Función para actualizar task definition
update_task_definition() {
    local service_key=$1
    local service_name=$2
    local task_def_name="${service_name}-task"
    
    log "Actualizando task definition para ${service_name}..."
    
    # Obtener la task definition actual
    local current_task_def=$(aws ecs describe-task-definition \
        --task-definition "${task_def_name}" \
        --query 'taskDefinition' \
        --output json 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        warn "No se pudo obtener la task definition ${task_def_name}, creando nueva..."
        return 1
    fi
    
    # Crear nueva task definition con health check
    local new_task_def=$(echo "${current_task_def}" | jq --argjson healthCheck "$(create_health_check_config)" '
        # Remover campos que no se necesitan para registro
        del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .placementConstraints, .compatibilities, .registeredAt, .registeredBy) |
        
        # Agregar health check al primer container
        .containerDefinitions[0].healthCheck = $healthCheck |
        
        # Asegurar que el puerto 80 esté expuesto
        if (.containerDefinitions[0].portMappings | map(select(.containerPort == 80)) | length) == 0 then
            .containerDefinitions[0].portMappings += [{"containerPort": 80, "protocol": "tcp"}]
        else
            .
        end
    ')
    
    # Registrar nueva task definition
    local new_revision=$(echo "${new_task_def}" | aws ecs register-task-definition \
        --cli-input-json file:///dev/stdin \
        --query 'taskDefinition.revision' \
        --output text)
    
    if [ $? -eq 0 ]; then
        log "✓ Task definition ${task_def_name}:${new_revision} registrada con health check"
        return 0
    else
        error "✗ Error al registrar task definition ${task_def_name}"
        return 1
    fi
}

# Función principal
main() {
    log "=== Iniciando actualización de health checks para servicios GKV ==="
    
    local success_count=0
    local total_count=${#SERVICES[@]}
    
    for service_key in "${!SERVICES[@]}"; do
        service_name="${SERVICES[$service_key]}"
        
        if update_task_definition "$service_key" "$service_name"; then
            ((success_count++))
        fi
        
        echo ""
    done
    
    log "=== Resumen ==="
    log "Servicios actualizados: ${success_count}/${total_count}"
    
    if [ $success_count -eq $total_count ]; then
        log "✓ Todos los servicios fueron actualizados exitosamente"
        exit 0
    else
        warn "⚠ Algunos servicios no pudieron ser actualizados"
        exit 1
    fi
}

# Verificar argumentos
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Uso: $0 [--dry-run]"
    echo ""
    echo "Este script actualiza las task definitions de ECS para agregar health checks"
    echo "a los servicios GKV de transporte."
    echo ""
    echo "Opciones:"
    echo "  --dry-run    Simula la ejecución sin hacer cambios reales"
    echo "  --help       Muestra esta ayuda"
    exit 0
fi

if [ "$1" = "--dry-run" ]; then
    warn "Modo DRY-RUN activado - no se harán cambios reales"
    # Aquí podrías agregar lógica para simular sin hacer cambios
fi

# Ejecutar función principal
main
