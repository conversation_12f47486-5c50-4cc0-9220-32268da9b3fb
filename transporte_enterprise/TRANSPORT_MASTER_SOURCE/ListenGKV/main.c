#include <unistd.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <sys/wait.h>
#include <pthread.h>
#include <sys/resource.h>

#include <cJSON.h>

#include <mongoose.h>
#include <service.h>
#include <dirent.h>

transport_cfg cfg;

char srvTagL[80 + 1];
int runningServerCount = 0;

char* LHOME = NULL;

time_t lastShown = 0;
pthread_mutex_t  lastShown_mutex;

/**********************************************/

// TODO
// Trap signals

/**********************************************/

void getConfiguration()
{
    char cfgFile[100 + 1];

    FILE *fd = NULL;

    sprintf(cfgFile, "%s/conf/params.cfg", LHOME);

    if ((fd = fopen(cfgFile, "r")) != NULL)
    {
        readConfigFile(fd, &cfg);
        fclose(fd);
    }
    else
    {
        logGW("Can't open config file: %s", cfgFile);
        logGW("Force exit!");
        exit(-1);
    }
}

int checkProcStop()
{
    char ctlFile[100 + 1];

    FILE *fd = NULL;

    sprintf(ctlFile, "%s/run/%s.cmd", LHOME, srvTagL);

    if ((fd = fopen(ctlFile, "r")) != NULL)
    {
        fclose(fd);
        return 1;
    }
    else
    {
        return 0;
    }
}

void writeErrorResponse(struct mg_connection *conn, int errCode, char *errText)
{
    cJSON *response = cJSON_CreateObject();
    cJSON_AddNumberToObject(response, "errCode", errCode);
    cJSON_AddStringToObject(response, "errText", errText);

    char *out = cJSON_PrintUnformatted(response);
    logGW("---- Response -----");
    logGW("%s", out);
    logGW("-------------------");
    syncLog();

    mg_printf_data(conn, out);
    free(out);

    cJSON_Delete(response);
}

void writeSuccessResponse(struct mg_connection *conn, cJSON *result)
{
    char *out = cJSON_PrintUnformatted(result);
    logGW("---- Response -----");
    logGW("%s", out);
    logGW("-------------------");
    syncLog();

    mg_printf_data(conn, out);
    free(out);
}

void setLastShown()
{
    pthread_mutex_lock(&lastShown_mutex);
    lastShown = time(NULL);
    pthread_mutex_unlock(&lastShown_mutex);
}

time_t getLastShown()
{
    pthread_mutex_lock(&lastShown_mutex);
    time_t t = lastShown;
    pthread_mutex_unlock(&lastShown_mutex);
    return t;
}

int processRequest(struct mg_connection *conn)
{
    char localBuf[MAX_LAWS_REQUEST + 1];

    logGW("==== Request ====");
    logGW("method: %s", conn->request_method);
    logGW("uri: %s", conn->uri);
    logGW("query_string: %s", (conn->query_string == NULL) ? "" : conn->query_string);
    logGW("---- Headers ----");

    int i;
    for (i = 0; i < conn->num_headers; i++)
    {
        logGW("name: %s  value: %s", conn->http_headers[i].name, conn->http_headers[i].value);
    }
    logGW("----------------");
    syncLog();

    logGW("content_len: %ld", conn->content_len); syncLog();
    if (conn->content_len < 0)
    {
        logGW("Invalid content lengh. Force exit!");
        logGW("==================");
        exit(-1);
    }

    if (strcmp(conn->uri, cfg.lgk_lmo_request_uri) == 0)
    {
        char errText[ERRTEXT_LEN + 1];

        setLastShown();

        mg_send_status(conn, 200);
        mg_send_header(conn, "Content-Disposition", "inline");
        mg_send_header(conn, "Content-Type", "application/json; charset=UTF-8");
        mg_send_header(conn, "Expires", "Mon, 26 Jul 1997 05:00:00 GMT");
        mg_send_header(conn, "Server", "LGK");

        if (conn->content_len <= MAX_LAWS_REQUEST)
        {
            strncpy(localBuf, conn->content, conn->content_len);
            localBuf[conn->content_len] = '\0';
            logGW("----------------");
            logGW("content: ->%s<-", localBuf);
            logGW("----------------");
            logGW("==================");
            syncLog();
        }
        else
        {
            snprintf(errText, ERRTEXT_LEN, "Request content length is too big (%ld > %ld)", conn->content_len, MAX_LAWS_REQUEST); errText[ERRTEXT_LEN] = '\0';
            writeErrorResponse(conn, 99, errText);
            logGW("FAILED: %s", errText);
            logGW("==================");
            syncLog();
            return MG_TRUE;
        }

        cJSON *param = cJSON_Parse(localBuf);

        while (1)
        {

            if (! param)
            {
                snprintf(errText, ERRTEXT_LEN, "Parse exception (%s)", cJSON_GetErrorPtr()); errText[ERRTEXT_LEN] = '\0';
                writeErrorResponse(conn, 100, errText);
                logGW("FAILED: %s", errText); syncLog();
                break;
            }

            if (param->type != cJSON_Array)
            {
                snprintf(errText, ERRTEXT_LEN, "Invalid json document. It must be a 'JSONArray'.");
                writeErrorResponse(conn, 101, errText);
                logGW("FAILED: %s", errText); syncLog();
                break;
            }

            int idx;
            cJSON *result = cJSON_CreateArray();

            for (idx = 0; idx < cJSON_GetArraySize(param); idx++)
            {
/*
 * XXX Agregar validaciones de estructura
 */
                cJSON *element     = cJSON_GetArrayItem(param, idx);
                cJSON *msisdn      = cJSON_GetObjectItem(element, "msisdn");
                cJSON *shortNumber = cJSON_GetObjectItem(element, "shortNumber");
                cJSON *message     = cJSON_GetObjectItem(element, "message");
                cJSON *operator    = cJSON_GetObjectItem(element, "operator");
                cJSON *appId       = cJSON_GetObjectItem(element, "appId");

                // return element
                cJSON *fld = cJSON_CreateObject();

                int ret = insRowLGK_MO(appId->valuestring, operator->valuestring, msisdn->valuestring, message->valuestring, cfg.lgk_sp_insert_mo);
                
				if (ret < 0)
				{
                    logGW("MO => [IGNORED: No url adapter for this app token] (%s -> %s) [%s|%s] [%s]", msisdn->valuestring, shortNumber->valuestring, operator->valuestring, appId->valuestring, message->valuestring); syncLog();
					// populate return element
					cJSON_AddStringToObject(fld, "errCode", "0");
					cJSON_AddStringToObject(fld, "errText", "");
				}
				else if (ret == 0)
                {
                    logGW("MO => [IGNORED: No company for this app token] (%s -> %s) [%s|%s] [%s]", msisdn->valuestring, shortNumber->valuestring, operator->valuestring, appId->valuestring, message->valuestring); syncLog();
					// populate return element
					cJSON_AddStringToObject(fld, "errCode", "101");
					cJSON_AddStringToObject(fld, "errText", "No company for this app token");
				}
				else if (ret == 1)
				{
                    logGW("MO => [OK] (%s -> %s) [%s|%s] [%s]", msisdn->valuestring, shortNumber->valuestring, operator->valuestring, appId->valuestring, message->valuestring); syncLog();
					// populate return element
					cJSON_AddStringToObject(fld, "errCode", "0");
					cJSON_AddStringToObject(fld, "errText", "");
				}
				else
				{
                    logGW("MO => [IGNORED: Multiple companies for this app token] (%s -> %s) [%s|%s] [%s]", msisdn->valuestring, shortNumber->valuestring, operator->valuestring, appId->valuestring, message->valuestring); syncLog();
					// populate return element
					cJSON_AddStringToObject(fld, "errCode", "201");
					cJSON_AddStringToObject(fld, "errText", "Multiple companies for this app token");
				}                

                // add return element to result
                cJSON_AddItemToArray(result, fld);
            }

            writeSuccessResponse(conn, result);
            cJSON_Delete(result);
            break;
        }

        if (param)
        {
            cJSON_Delete(param);
        }

        return MG_TRUE;
    }
    else if (strcmp(conn->uri, cfg.lgk_ack_request_uri) == 0)
    {
        char errText[ERRTEXT_LEN + 1];

        setLastShown();

        mg_send_status(conn, 200);
        mg_send_header(conn, "Content-Disposition", "inline");
        mg_send_header(conn, "Content-Type", "application/json; charset=UTF-8");
        mg_send_header(conn, "Expires", "Mon, 26 Jul 1997 05:00:00 GMT");
        mg_send_header(conn, "Server", "LGK");

        if (conn->content_len <= MAX_LAWS_REQUEST)
        {
            strncpy(localBuf, conn->content, conn->content_len);
            localBuf[conn->content_len] = '\0';
            logGW("----------------");
            logGW("content: ->%s<-", localBuf);
            logGW("----------------");
            logGW("==================");
            syncLog();
        }
        else
        {
            snprintf(errText, ERRTEXT_LEN, "Request content length is too big (%ld > %ld)", conn->content_len, MAX_LAWS_REQUEST); errText[ERRTEXT_LEN] = '\0';
            writeErrorResponse(conn, 99, errText);
            logGW("FAILED: %s", errText);
            logGW("==================");
            syncLog();
            return MG_TRUE;
        }

        cJSON *param = cJSON_Parse(localBuf);

        while (1)
        {

            if (! param)
            {
                snprintf(errText, ERRTEXT_LEN, "Parse exception (%s)", cJSON_GetErrorPtr()); errText[ERRTEXT_LEN] = '\0';
                writeErrorResponse(conn, 100, errText);
                logGW("FAILED: %s", errText); syncLog();
                break;
            }

            if (param->type != cJSON_Array)
            {
                snprintf(errText, ERRTEXT_LEN, "Invalid json document. It must be a 'JSONArray'.");
                writeErrorResponse(conn, 101, errText);
                logGW("FAILED: %s", errText); syncLog();
                break;
            }

            int idx;
            int pl = strlen(cfg.app_prefix);

            cJSON *result = cJSON_CreateArray();

            for (idx = 0; idx < cJSON_GetArraySize(param); idx++)
            {
/*
 * XXX Agregar validaciones de estructura
 */

                cJSON *element = cJSON_GetArrayItem(param, idx);

                cJSON *appId         = cJSON_GetObjectItem(element, "appId");
                cJSON *msgId         = cJSON_GetObjectItem(element, "msgId");
                cJSON *idGKV         = cJSON_GetObjectItem(element, "idGKV");
                cJSON *msisdn        = cJSON_GetObjectItem(element, "msisdn");
                cJSON *shortNumber   = cJSON_GetObjectItem(element, "shortNumber");
                cJSON *status        = cJSON_GetObjectItem(element, "status");
                cJSON *cause         = cJSON_GetObjectItem(element, "cause");
                cJSON *confirmedTime = cJSON_GetObjectItem(element, "confirmedTime");
                cJSON *operator      = cJSON_GetObjectItem(element, "operator");

                int id = 0;
                char *p = strstr(msgId->valuestring, cfg.app_prefix);
                if (p != NULL)
                {
                    id = atoi(p + pl);
                }
                
                int sp_result;

                if (id == 0)
                {
                    logGW("WARNING: Invalid msgId ->%s<-", msgId->valuestring); syncLog();
                    sp_result = 0;
                }
                else if (strcmp(status->valuestring, "DELIVERED") == 0)
                {
                    modRowLGK(id, "CONFIRMED", operator->valuestring, 0, cause->valuestring, NULL, cfg.lgk_sp_update_mt);
					sp_result = insRowLGK_ACK(appId->valuestring, msgId->valuestring, idGKV->valuestring, id, msisdn->valuestring, shortNumber->valuestring, operator->valuestring, status->valuestring, cause->valuestring, cfg.lgk_sp_insert_ack);
                }
                else if (strcmp(status->valuestring, "UNDELIVERED") == 0)
                {
                    modRowLGK(id, "FAILED", operator->valuestring, 0, cause->valuestring, NULL, cfg.lgk_sp_update_mt);
					sp_result = insRowLGK_ACK(appId->valuestring, msgId->valuestring, idGKV->valuestring, id, msisdn->valuestring, shortNumber->valuestring, operator->valuestring, status->valuestring, cause->valuestring, cfg.lgk_sp_insert_ack);
                }
                else
                {
                    logGW("WARNING: Invalid status ->%s<-", status->valuestring); syncLog();
                    sp_result = 0;
                }

				if (sp_result == 0)
				{
					logGW("ACK => [REJECTED] idMT: %d appId: %s msgId: %s idGKV: %s msisdn: %s shortNumber: %s status: %s cause: %s confirmedTime: %s operator: %s", id, appId->valuestring, msgId->valuestring, idGKV->valuestring, msisdn->valuestring, shortNumber->valuestring, status->valuestring, cause->valuestring, confirmedTime->valuestring, operator->valuestring);
				}
				else if (sp_result < 0)
				{
					logGW("ACK => [IGNORED] idMT: %d appId: %s msgId: %s idGKV: %s msisdn: %s shortNumber: %s status: %s cause: %s confirmedTime: %s operator: %s", id, appId->valuestring, msgId->valuestring, idGKV->valuestring, msisdn->valuestring, shortNumber->valuestring, status->valuestring, cause->valuestring, confirmedTime->valuestring, operator->valuestring);
				}
				else
				{
					logGW("ACK => [OK] idMT: %d appId: %s msgId: %s idGKV: %s msisdn: %s shortNumber: %s status: %s cause: %s confirmedTime: %s operator: %s", id, appId->valuestring, msgId->valuestring, idGKV->valuestring, msisdn->valuestring, shortNumber->valuestring, status->valuestring, cause->valuestring, confirmedTime->valuestring, operator->valuestring);
				}
                syncLog();

                // populate return element
                cJSON *fld;
                cJSON_AddItemToArray(result, fld = cJSON_CreateObject());
                cJSON_AddStringToObject(fld, "errCode", "0");
                cJSON_AddStringToObject(fld, "errText", "");
            }

            writeSuccessResponse(conn, result);
            cJSON_Delete(result);
            break;

        }

        if (param)
        {
            cJSON_Delete(param);
        }

        return MG_TRUE;
    }
    else if (strcmp(conn->uri, cfg.lgk_rmt_request_uri) == 0)
    {
        char errText[ERRTEXT_LEN + 1];

        setLastShown();

        mg_send_status(conn, 200);
        mg_send_header(conn, "Content-Disposition", "inline");
        mg_send_header(conn, "Content-Type", "application/json; charset=UTF-8");
        mg_send_header(conn, "Expires", "Mon, 26 Jul 1997 05:00:00 GMT");
        mg_send_header(conn, "Server", "LGK");

        if (conn->content_len <= MAX_LAWS_REQUEST)
        {
            strncpy(localBuf, conn->content, conn->content_len);
            localBuf[conn->content_len] = '\0';
            logGW("----------------");
            logGW("content: ->%s<-", localBuf);
            logGW("----------------");
            logGW("==================");
            syncLog();
        }
        else
        {
            snprintf(errText, ERRTEXT_LEN, "Request content length is too big (%ld > %ld)", conn->content_len, MAX_LAWS_REQUEST); errText[ERRTEXT_LEN] = '\0';
            writeErrorResponse(conn, 99, errText);
            logGW("FAILED: %s", errText);
            logGW("==================");
            syncLog();
            return MG_TRUE;
        }

        cJSON *param = cJSON_Parse(localBuf);

        while (1)
        {

            if (! param)
            {
                snprintf(errText, ERRTEXT_LEN, "Parse exception (%s)", cJSON_GetErrorPtr()); errText[ERRTEXT_LEN] = '\0';
                writeErrorResponse(conn, 100, errText);
                logGW("FAILED: %s", errText); syncLog();
                break;
            }

            if (param->type != cJSON_Array)
            {
                snprintf(errText, ERRTEXT_LEN, "Invalid json document. It must be a 'JSONArray'.");
                writeErrorResponse(conn, 101, errText);
                logGW("FAILED: %s", errText); syncLog();
                break;
            }

            int idx;
            int pl = strlen(cfg.app_prefix);

            cJSON *result = cJSON_CreateArray();

            for (idx = 0; idx < cJSON_GetArraySize(param); idx++)
            {
/*
 * XXX Agregar validaciones de estructura
 */

                cJSON *element = cJSON_GetArrayItem(param, idx);

                cJSON *appId         = cJSON_GetObjectItem(element, "appId");
                cJSON *msgId         = cJSON_GetObjectItem(element, "msgId");
                cJSON *idGKV         = cJSON_GetObjectItem(element, "idGKV");
                cJSON *msisdn        = cJSON_GetObjectItem(element, "msisdn");
                cJSON *shortNumber   = cJSON_GetObjectItem(element, "shortNumber");
                cJSON *status        = cJSON_GetObjectItem(element, "status");
                cJSON *cause         = cJSON_GetObjectItem(element, "cause");
                cJSON *confirmedTime = cJSON_GetObjectItem(element, "confirmedTime");
                cJSON *operator      = cJSON_GetObjectItem(element, "operator");

                int id = 0;
                char *p = strstr(msgId->valuestring, cfg.app_prefix);
                if (p != NULL)
                {
                    id = atoi(p + pl);
                }
                
                int sp_result;

                if (id == 0)
                {
                    logGW("WARNING: Invalid msgId ->%s<-", msgId->valuestring); syncLog();
                    sp_result = 0;
                }
                else if (strcmp(status->valuestring, "PUSHED") == 0)
                {
                    modRowLGK(id, "PUSHED", operator->valuestring, 0, cause->valuestring, NULL, cfg.lgk_sp_update_mt);
					sp_result = insRowLGK_ACK(appId->valuestring, msgId->valuestring, idGKV->valuestring, id, msisdn->valuestring, shortNumber->valuestring, operator->valuestring, status->valuestring, cause->valuestring, cfg.lgk_sp_insert_ack);
                }
                else if (strcmp(status->valuestring, "NOPUSHED") == 0)
                {
                    modRowLGK(id, "NOPUSHED", operator->valuestring, 0, cause->valuestring, NULL, cfg.lgk_sp_update_mt);
					sp_result = insRowLGK_ACK(appId->valuestring, msgId->valuestring, idGKV->valuestring, id, msisdn->valuestring, shortNumber->valuestring, operator->valuestring, status->valuestring, cause->valuestring, cfg.lgk_sp_insert_ack);
                }
                else if (strcmp(status->valuestring, "EXPIRED") == 0)
                {
                    modRowLGK(id, "FAILED", operator->valuestring, 0, cause->valuestring, NULL, cfg.lgk_sp_update_mt);
					sp_result = insRowLGK_ACK(appId->valuestring, msgId->valuestring, idGKV->valuestring, id, msisdn->valuestring, shortNumber->valuestring, operator->valuestring, status->valuestring, cause->valuestring, cfg.lgk_sp_insert_ack);
                }
                else
                {
                    logGW("WARNING: Invalid status ->%s<-", status->valuestring); syncLog();
                    sp_result = 0;
                }

				if (sp_result == 0)
				{
					logGW("RMT => [REJECTED] idMT: %d appId: %s msgId: %s idGKV: %s msisdn: %s shortNumber: %s status: %s cause: %s confirmedTime: %s operator: %s", id, appId->valuestring, msgId->valuestring, idGKV->valuestring, msisdn->valuestring, shortNumber->valuestring, status->valuestring, cause->valuestring, confirmedTime->valuestring, operator->valuestring);
				}
				else if (sp_result < 0)
				{
					logGW("RMT => [IGNORED] idMT: %d appId: %s msgId: %s idGKV: %s msisdn: %s shortNumber: %s status: %s cause: %s confirmedTime: %s operator: %s", id, appId->valuestring, msgId->valuestring, idGKV->valuestring, msisdn->valuestring, shortNumber->valuestring, status->valuestring, cause->valuestring, confirmedTime->valuestring, operator->valuestring);
				}
				else
				{
					logGW("RMT => [OK] idMT: %d appId: %s msgId: %s idGKV: %s msisdn: %s shortNumber: %s status: %s cause: %s confirmedTime: %s operator: %s", id, appId->valuestring, msgId->valuestring, idGKV->valuestring, msisdn->valuestring, shortNumber->valuestring, status->valuestring, cause->valuestring, confirmedTime->valuestring, operator->valuestring);
				}
				syncLog();

                // populate return element
                cJSON *fld;
                cJSON_AddItemToArray(result, fld = cJSON_CreateObject());
                cJSON_AddStringToObject(fld, "errCode", "0");
                cJSON_AddStringToObject(fld, "errText", "");
            }

            writeSuccessResponse(conn, result);
            cJSON_Delete(result);
            break;

        }

        if (param)
        {
            cJSON_Delete(param);
        }

        return MG_TRUE;
    }
    else
    {
        setLastShown();

        mg_send_status(conn, 404);
        mg_send_header(conn, "Content-Type", "text/plain");
        mg_printf_data(conn, "Page not found.");
        logGW("FAILED: Page not found.");
        syncLog();
        return MG_TRUE;
    }

}

static int ev_handler(struct mg_connection *conn, enum mg_event ev)
{
    if (ev == MG_REQUEST)
    {
        return processRequest(conn);
    }
    else if (ev == MG_AUTH)
    {
        return MG_TRUE;
    }
    else
    {
        return MG_FALSE;
    }
}
 
static void *serve(void *server)
{
    __sync_fetch_and_add(&runningServerCount, 1 );
    char user_data[10 + 1];
    mg_get_user_data(server, user_data);
    logGW("Server [%s] started.", user_data); syncLog();
    while (! checkProcStop())
    {
        mg_poll_server((struct mg_server *) server, 500);
    }
    logGW("Server [%s] stopped.", user_data); syncLog();
    __sync_fetch_and_sub(&runningServerCount, 1 );
    return NULL;
}

/**********************************************/

int main(int argc, char **argv)
{
    int CICLES = 20;

    LHOME = getenv("LHOME");

    if (LHOME == NULL)
    {
        printf("Undefined LHOME environment variable.\n");
	LHOME="/home/<USER>/GKV";
        //exit(-1);
    }

    DIR* dir = opendir(LHOME);
    if (dir)
    {  
         closedir(dir);
    }
    else   
    {
        printf("Invalid LHOME environment variable. Directory does not exists: %s\n", LHOME);
        exit(-1);
    }  
       
    if (argc != 2)
    {
        printf("Uso: %s <tag>\n", argv[0]);
        printf("Invalid argument.\n");
        exit(-1);
    }

    strcpy(srvTagL, argv[1]);

    initLog(LHOME, srvTagL);

    /***********************************/
    /* Genera un core si se requiere */
    struct rlimit r;

    r.rlim_cur = RLIM_INFINITY;
    r.rlim_max = RLIM_INFINITY;
    if (setrlimit(RLIMIT_CORE, &r) == -1)
    {
        logGW("Error in setrlimit()\n"); syncLog();
        return -1;
    }
    /***********************************/

    logGW("Starting process %s.", srvTagL);
    logGW("LHOME: '%s'", LHOME);
    syncLog();

    /* read configuration file */
    getConfiguration();
   
    /*************/

    /* Verificamos que no se este ejecutando otra instancia en el proceso padre */ 
    if (lockPID(LHOME, srvTagL) != 0)
    {
        logGW("Couldn't lock pid file. Maybe another instance is running. Process [%d] aborted.", getpid());
        exit(-1);
    }  
#ifdef __STAND_ALONE__
    /* Become a daemon */
    switch (fork())
    {
        case 0:
            break;
        case -1:
            logGW("Unable to become a daemon");
        default:
            exit(0);
    };

    if (setsid () == -1)
    {
        perror("setsid failed.");
        exit(-1);
    }  
 
    /* Bloqueamos el archivo pid de esta instancia en el proceso hijo */ 
    sleep(1); 
    if (lockPID(LHOME, srvTagL) != 0)
    {
        logGW("Couldn't lock pid file. Maybe another instance is running. Process [%d] aborted.", getpid());
        exit(-1);
    }  
#endif
    /* Modificamos a _IOLBF (line buffered) los buffers STDOUT y STDERR */
    setlinebuf(stdout);
    setlinebuf(stderr);

    /* show configuration file */
    showConfig(&cfg);

    /* Start the httpd daemon */
    int max_srv = cfg.lgk_max_srv;
    char label[max_srv][10 + 1];
    struct mg_server *server[max_srv];
    int i;

    /* Initialization */
    attachQLGK(&cfg);
    pthread_mutex_init(&lastShown_mutex, NULL);

    for (i = 0; i < max_srv; i++)
    {
        sprintf(label[i], "Srv-%03d", (i + 1));
        server[i] = mg_create_server((void *) label[i], ev_handler);
    }

    // Make all servers listen on the same sockets
    char thePort[12 + 1];
    sprintf(thePort, "%d", cfg.lgk_port);
    mg_set_option(server[0], "listening_port", thePort);
    for (i = 1; i < max_srv; i++)
    {
        mg_copy_listeners(server[0], server[i]);
    }

    // IMPORTANT: NEVER LET DIFFERENT THREADS HANDLE THE SAME SERVER.
    for (i = 0; i < max_srv; i++)
    {
        mg_start_thread(serve, server[i]);
    }

    for (i = 1; i <= CICLES && runningServerCount < max_srv; i++)
    {
        logGW("Waiting for all thread servers to start ... (%d/%d)", i, CICLES);
        sleep(1);
    }
    if (runningServerCount == max_srv)
    {
        logGW("All thread servers started.");
    }
    else
    {
        logGW("Timeout. Failed to start all servers. Force exit.");
        exit(-1);
    }

    logGW("httpd server listening on %s:%s", cfg.lgk_ip, thePort); syncLog();

    while (! checkProcStop())
    {
        if (time(NULL) - getLastShown() >= cfg.lgk_show_wait)
        {
            logGW("Waiting for request to procesed ..."); syncLog();
            setLastShown();
        }
        sleep(cfg.lgk_poll_time);
    }

    for (i = 1; i <= CICLES && runningServerCount > 0; i++)
    {
        logGW("Waiting for all thread servers to stop ... (%d/%d)", i, CICLES); syncLog();
        sleep(1);
    }
    if (runningServerCount == 0)
    {
        logGW("All thread servers stopped."); syncLog();
    }
    else
    {
        logGW("Timeout. Force stop servers."); syncLog();
    }

    logGW("Process stopped.");
    syncLog();

	/* Free resources */
	detachQLGK();

    return 0;
}

/* ######################## EOF ############################ */
