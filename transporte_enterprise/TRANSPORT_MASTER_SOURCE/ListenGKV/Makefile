# Makefile for listenGKV

CC         = clang -ggdb
CCFLAGS    = -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DMONGOOSE_ENABLE_THREADS -DNS_STACK_SIZE=10485760 -O -Wall -Wno-pointer-sign
CCINCLUDES = -I. -I../Common

LD         = $(CC)
LDFLAGS    = 
LDLIBS     = -lpthread -lm -L/usr/lib64/mysql/ -lmysqlclient

all:  ./listenGKV

main.o: main.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c main.c

mongoose.o: mongoose.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c mongoose.c

cJSON.o: cJSON.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c cJSON.c

service.o: ../Common/service.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c ../Common/service.c

listenGKV: main.o cJSON.o mongoose.o service.o
	$(LD) -o $@ main.o cJSON.o mongoose.o service.o $(LDFLAGS) $(LDLIBS)
	strip listenGKV

clean:
	rm -f *.o listenGKV
