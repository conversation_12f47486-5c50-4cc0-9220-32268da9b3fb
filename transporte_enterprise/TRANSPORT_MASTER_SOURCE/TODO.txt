TODO:

2018-09-23: Limitar el parametro MAX_BATCH_SIZE = 1000 y verificar config.

2018-09-23: Limitar el parametro MAX_THREADS = 10 y verificar config.

2018-09-21: Monitorear la cantidad de hilos vs ulimit -u

2018-09-20: Considerar pool de conexiones a la DB para los procesos de despacho.

2018-09-20: Considerar logs diferenciados por hilos.

2018-09-20: Despachador adaptativo, dependiendo del encolamiento crear o quitar hilos

2018-09-20: Considerar un limite para el numero maximo de request simultaneos al GKV.

2018-09-20: Considerar RPM vs RPS (requests por minuto vs request por segundo)

2018-09-20: Logear estadisticas de despacho para afinar los parametros.

2018-09-20: Considerar grupos de despacho para separar cpompañias.

----------------------
            
2018-08-20: Generalizar -> void format_msisdn(char *msisdn, char *phone)
 
2018-08-20: Manejo de errores en los despachadores. Ahora si hay error general
            se fuerza la salida.
            
2018-08-20: Logs para dispatchMO

----------------------

WARARAS:


BUFFER:

/***
void destroyDispatchThread(int company, int queue)
{
	pthread_mutex_lock(&ctlThreads_mutex);

    controlDMT *ctl = ctlThreads;
    
    if (ctl == NULL)
    {
		// nothing to do
	}
	else if (ctl->company == company && ctl->queue == queue)
	{
		ctlThreads = ctl->next;
		free(ctl);
	}
	else
	{
		controlDMT *prv = ctl;
		
		ctl = ctl->next;	
		while (ctl != NULL)
		{
			if (ctl->company == company && ctl->queue == queue)
			{
				prv->next = ctl->next;
				free(ctl);
				break;
			}
			prv = ctl;
			ctl = ctl->next;
		}
	}
	
	pthread_mutex_unlock(&ctlThreads_mutex);
}
***/
