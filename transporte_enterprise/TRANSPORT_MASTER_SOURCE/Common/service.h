#include <time.h>
#include <mysql/mysql.h>

#define MAX_LOG_LENGTH            10240       /*  10 KB */
#define CONFIG_MAX_LEN            1024        /*   1 KB */
#define MAX_LAWS_REQUEST          1048576L    /*   1 MB */
#define ERRTEXT_LEN               255
#define DISPATCH_STACK_SIZE       2097152     /*   2 MB */

#define MAX_COMPANIES             100

typedef struct
{
    // Common
    char            app_dbhost[100 + 1];
    char            app_dbname[100 + 1];
    char            app_dbuser[100 + 1];
    char            app_dbpass[100 + 1];
    unsigned int    app_dbport;
    char            app_prefix[ 20 + 1];

    // LGK
    char lgk_ip[15 + 1];
    int  lgk_port;
    char lgk_lmo_request_uri[100 + 1];
    char lgk_ack_request_uri[100 + 1];
    char lgk_rmt_request_uri[100 + 1];
    char lgk_sp_update_mt[80 + 1];
    char lgk_sp_insert_mo[80 + 1];
    char lgk_sp_insert_ack[80 + 1];
    int  lgk_max_srv;
    int  lgk_poll_time;
    int  lgk_show_wait;

    // DGK
    char dgk_laws_url[255 + 1];
    int  dgk_threads_count;
    char dgk_sp_status_mt[80 + 1];
    char dgk_sp_select_mt[80 + 1];
    char dgk_sp_update_mt[80 + 1];
    int  dgk_inactive_time;
    int  dgk_read_wait;
    int  dgk_show_wait;
    int  dgk_batch_size;
    int  dgk_batch_rps;

    // DMO
    int  dmo_threads_count;
    char dmo_sp_status_mo[80 + 1];
	char dmo_sp_select_mo[80 + 1];
	char dmo_sp_update_mo[80 + 1];
	char dmo_user[80 + 1];
	char dmo_pass[80 + 1];
    int  dmo_inactive_time;
	int  dmo_read_wait;
	int  dmo_show_wait;
	int  dmo_batch_size;
	int  dmo_batch_rps;

    // DACK
    int  dack_threads_count;
    char dack_sp_status_ack[80 + 1];
	char dack_sp_select_ack[80 + 1];
	char dack_sp_update_ack[80 + 1];
	char dack_user[80 + 1];
	char dack_pass[80 + 1];
    int  dack_inactive_time;
	int  dack_read_wait;
	int  dack_show_wait;
	int  dack_batch_size;
	int  dack_batch_rps;

    // MSD
    int  msd_read_wait;
    int  msd_show_wait;
    int  msd_batch_size;
    int  msd_post_time;
    char msd_sp_blacklist[80 + 1];
    char msd_sp_start_campaign[80 + 1];
    char msd_sp_move_campaign[80 + 1];
    char msd_sp_end_campaign[80 + 1];
    char msd_sp_post_campaign[80 + 1];

} transport_cfg;

typedef struct
{
    MYSQL            conn;
    pthread_mutex_t  dbaccess_mutex;
    time_t           lastAccess;
} connMYSQL;

typedef struct _controlDMT
{
    int        company;
    int        queue;
    char       gkv_token[45 + 1];
    pthread_t  thread;
    char       threadStatus;      // A: active  I: inactive  S: stopped
    struct _controlDMT *next;
} controlDMT;

typedef struct
{
    int  company;
    char gkv_token[45 + 1];
    int  count;
} statusQDMT;

typedef struct
{
    int  id;
    char recipientId[30 + 1];
    char msgText[255 + 1];
    char cc[100 + 1];
} msgQDMT;

typedef struct _controlDMO
{
    int        company;
    int        queue;
    char       url_adapter_mo[255 + 1];
    pthread_t  thread;
    char       threadStatus;      // A: active  I: inactive  S: stopped
    struct _controlDMO *next;
} controlDMO;

typedef struct
{
    int  company;
    char url_adapter_mo[255 + 1];
    int  count;
} statusQDMO;

typedef struct
{
    int  id;
    int  id_company;
    char carrier[30 + 1];
    char phone[30 + 1];
    char msgtext[255 + 1];
    int  delivery_count;
    char status[30 + 1];
    int  time_retry;
    int  max_retry;
} msgQDMO;

typedef struct _controlDACK
{
    int        company;
    int        queue;
    char       url_adapter_ack[255 + 1];
    pthread_t  thread;
    char       threadStatus;      // A: active  I: inactive  S: stopped
    struct _controlDACK *next;
} controlDACK;

typedef struct
{
    int  company;
    char url_adapter_ack[255 + 1];
    int  count;
} statusQDACK;

typedef struct
{
    int  id;
	char clientCode[45 +1];
    char mobileNumber[30 + 1];
    char status[30 + 1];
    char message[255 + 1];
    char carrier[100 + 1];
    char date[19 + 1];
    int  delivery_count;
    int  time_retry;
    int  max_retry;
} msgQDACK;

extern int  lockPID(const char *path, const char *tag);
extern void initLog(const char *path, const char *tag);
extern int  logGW(const char* format, ...);
extern int  syncLog();
extern void traceExit(int code, char *info);
extern void controlThrot(int rate);
extern void format_msisdn(char *msisdn, char *phone);

extern void readConfigFile(FILE *fd, transport_cfg *cfg);
extern void showConfig(transport_cfg *cfg);

extern void attachQDMT(transport_cfg *cfg);
extern int  getStatusDMT(statusQDMT *statusDMT, char *store_procedure);
extern int  getRowsDMT(msgQDMT *queueDMT, int company, int queue, int queue_size, int batch_size, char *store_procedure);
extern int  modRowDMT(int id, char *status, char *operator, int errCode, char *errText, char *gkv_id, char *store_procedure);
extern void detachQDMT();

extern void attachQDMO(transport_cfg *cfg);
extern int  getStatusDMO(statusQDMO *statusDMO, char *store_procedure);
extern int  getRowsDMO(msgQDMO *queueDMO, int company, int queue, int queue_size, int batch_size, char *store_procedure);
extern int  modRowDMO(int id, char *status, int delta, int errCode, char *errText, char *store_procedure);
extern void detachQDMO();

extern void attachQDACK(transport_cfg *cfg);
extern int  getStatusDACK(statusQDACK *statusDACK, char *store_procedure);
extern int  getRowsDACK(msgQDACK *queueDACK, int company, int queue, int queue_size, int batch_size, char *store_procedure);
extern int  modRowDACK(int id, char *status, int delta, int errCode, char *errText, char *store_procedure);
extern void detachQDACK();

extern void attachQLGK(transport_cfg *cfg);
extern int  insRowLGK_MO(char *gkv_token, char *operator, char *msisdn, char *message, char *store_procedure);
extern int  insRowLGK_ACK(char *app_id, char *msg_id, char *id_gkv, int ref_mt_id, char *phone, char *short_number, char *carrier, char *ack_status, char *ack_reason, char *store_procedure);
extern int  modRowLGK(int id, char *status, char *operator, int errCode, char *errText, char *gkv_id, char *store_procedure);
extern void detachQLGK();

extern void attachQMSD(transport_cfg *cfg);
extern int  checkBlackList(int limit, char *store_procedure);
extern int  startCampaign(char *store_procedure);
extern int  moveCampaign(char *store_procedure);
extern int  endCampaign(char *store_procedure);
extern int  postCampaign(int delta, char *store_procedure);
extern void detachQMSD();

/************** EOF **************/
