#include <errno.h>
#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/time.h>
#include <ctype.h>
#include <service.h>

pthread_mutex_t  log_mutex;

char home[255 + 1];
char srvTag[80 + 1];
FILE *fdLog;
char logSuffix[9] = "";

connMYSQL QUEUEconn;

/*************************************************/

int lockPID(const char *path, const char *tag)
{
    FILE *fd;
    char pidFile[255];
    sprintf(pidFile, "%s/run/%s.pid", path, tag);

    if ((fd = fopen(pidFile, "a")) == NULL)
    {
        return -1;
    }

    if (lockf(fileno(fd), F_TLOCK, 0) != 0)
    {
        fclose(fd);
        return -1;
    }

    fclose(fd);

    if ((fd = fopen(pidFile, "w")) == NULL)
    {
        return -1;
    }

    if (lockf(fileno(fd), F_TLOCK, 0) != 0)
    {
        fclose(fd);
        return -1;
    }

    fprintf(fd, "%d", getpid());
    fflush(fd);

    return 0;
}

void initLog(const char *path, const char *tag)
{
    strcpy(home, path);
    strcpy(srvTag, tag);
    pthread_mutex_init(&log_mutex, NULL);
}

int logGW(const char* format, ...)
{
    va_list ap;
    char localbuff[MAX_LOG_LENGTH + 1];
    //char logFile[255];

    char timeSuffix[9];
    char strDateTime[20];
    struct tm *datetime = NULL;
    time_t _t_ = time(NULL);

    datetime = localtime(&_t_);

    sprintf(strDateTime, "%04d-%02d-%02d %02d:%02d:%02d", datetime->tm_year + 1900, datetime->tm_mon + 1, datetime->tm_mday, datetime->tm_hour, datetime->tm_min, datetime->tm_sec);
    sprintf(timeSuffix, "%04d%02d%02d", datetime->tm_year + 1900, datetime->tm_mon + 1, datetime->tm_mday);

    /* lock log mutex */
    pthread_mutex_lock(&log_mutex);

/*  if (fdLog)
    {
        fseek(fdLog, 0L, SEEK_END);
    }
*/

#if 0 
     if (strcmp(logSuffix, timeSuffix) != 0)
    {
        if (fdLog != NULL)
        {
            fprintf(fdLog, "==> LOG FILE CLOSED FOR ROTATING <==\n");
            fflush(fdLog);
            fclose(fdLog);
        }

        strcpy(logSuffix, timeSuffix);
        sprintf(logFile, "%s/log/%s.%s.log", home, srvTag, logSuffix);
        if ((fdLog = fopen(logFile, "a")) == NULL)
        {
            printf("Can't open log file: %s\n", logFile);
            /* unlock log mutex */
            pthread_mutex_unlock(&log_mutex);
            exit(-1);
        }
        fseek(fdLog, 0L, SEEK_END);
        fprintf(fdLog, "==> ROTATING LOG NEW FILE OPEN <==\n");
        fflush(fdLog);
    }
#endif 
    if (format != NULL)
    {
        int ret;

        memset(localbuff, 0, MAX_LOG_LENGTH + 1);
        va_start(ap, format);
        ret = vsnprintf(localbuff, MAX_LOG_LENGTH, format, ap);
        va_end(ap);

        localbuff[MAX_LOG_LENGTH] = '\0';

        fprintf(stdout, "[%s] %s\n", strDateTime, localbuff);

        if (ret >= MAX_LOG_LENGTH)
        {
            fprintf(stdout, "... <truncated>\n");
            fflush(stdout);
        }
    }
    else
    {
        printf("WARNING: Can't log a null format.\n");
        fflush(stdout);
    }

    /* unlock log mutex */
    pthread_mutex_unlock(&log_mutex);

    return 0;
}

int syncLog()
{
    if (fdLog != NULL)
    {
        fflush(stdout);
    }
    return 0;
}

void traceExit(int code, char *info)
{
    printf("traceExit: %s\n", info);
    exit(code);
}

void controlThrot(int rate)
{
    static time_t slot = 0;
    static int procCount = 0;

    time_t now = time(NULL);
   
    procCount++;
   
    if (now == slot)
    {
        if (procCount >= rate)
        {
            static struct timeval start;
            struct timespec ts;
           
            gettimeofday(&start, 0);
           
            ts.tv_sec  = 0;
            ts.tv_nsec = (1000000 - start.tv_usec) * 1000;
            while (nanosleep(&ts, &ts))
            {
                if (errno != EINTR)
                {
                    break;
                }  
            }  
           
            slot = time(NULL);
            procCount = 0;
        }  
    }  
    else
    {
        slot = now;
        procCount = 1;
    }  
   
}

// Se debe generalizar !!!
void format_msisdn(char *msisdn, char *phone)
{
    int l = strlen(phone);

    if (l == 8)
    {
        sprintf(msisdn, "569%s", phone);
    }
    else if (l == 9)
    {
        sprintf(msisdn, "56%s", phone);
    }
    else
    {
        strcpy(msisdn, phone);
    }
}

/*************************************************/

int isEOL(char c)
{
    if (c == '\n')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int isREM(char c)
{
    if (c == '#')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int isTrim(char c)
{
    if (isblank(c))
    {
        return 1;
    }
    else if (c == '\r')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int isKey(char c)
{
    if (isalnum(c))
    {
        return 1;
    }
    else if (c == '-')
    {
        return 1;
    }
    else if (c == '_')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int isDelim(char c)
{
    if (c == '=')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int isSingleQuote(char c)
{
    if (c == '\'')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int isDoubleQuote(char c)
{
    if (c == '"')
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int isVal(char c)
{
    if (isalnum(c))
    {
        return 1;
    }
    else if (ispunct(c))
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int parseLine(char *buf, char *key, char *val)
{
    char c;
    int i = 0, s = 0, l = strlen(buf), k = 0, v = 0;

    while (i < l)
    {
        c = buf[i];

        switch (s)
        {
            case 0:

                if ( ! isTrim(c) )
                {
                    if (isEOL(c))
                    {
                        return 0;
                    }
                    else if (isREM(c))
                    {
                        return 0;
                    }
                    else if (isKey(c))
                    {
                        s = 1;
                        key[k++] = c;
                        key[k]   = '\0';
                    }
                    else
                    {
                        logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                        return -1;
                    }
                }

		break;

            case 1:

                if (isKey(c))
                {
                    key[k++] = c;
                    key[k]   = '\0';
                }
                else if (isTrim(c))
                {
                    s = 2;
                }
                else if (isDelim(c))
                {
                    s = 3;
                }
                else
                {
                    logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                    return -1;
                }

		break;

            case 2:

                if ( ! isTrim(c) )
                {
                    if (isDelim(c))
                    {
                        s = 3;
                    }
                    else
                    {
                        logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                        return -1;
                    }
                }

		break;

            case 3:

                if ( ! isTrim(c) )
                {
                    if (isEOL(c))
                    {
                        val[0]   = '\0';
                        return 1;
                    }
                    else if (isREM(c))
                    {
                        val[0]   = '\0';
                        return 1;
                    }
                    else if (isSingleQuote(c))
                    {
                        val[0]   = '\0';
                        s = 10;
                    }
                    else if (isDoubleQuote(c))
                    {
                        val[0]   = '\0';
                        s = 20;
                    }
                    else if (isVal(c))
                    {
                        s = 4;
                        val[v++] = c;
                        val[v]   = '\0';
                    }
                    else
                    {
                        logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                        return -1;
                    }
                }

                break;

            case 4:

                if (isVal(c))
                {
                    val[v++] = c;
                    val[v]   = '\0';
                }
                else if (isTrim(c))
                {
                    s = 5;
                }
                else if (isEOL(c))
                {
                    return 1;
                }
                else if (isREM(c))
                {
                    return 1;
                }
                else
                {
                    logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                    return -1;
                }

                break;

            case 5:

                if ( ! isTrim(c) )
                {
                    if (isEOL(c))
                    {   
                        return 1;
                    }   
                    else if (isREM(c))
                    {   
                        return 1;
                    }
                    else
                    {
                        logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                        return -1;
                    }
                }

                break;

            case 10:

                if ( isSingleQuote(c))
                {
                    val[v] = '\0';
                    s = 5;
                }
                else
                {
                    if (isEOL(c))
                    {
                        logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                        logGW("Unclosed single quote.");
                        return -1;
                    }
                    else if (isREM(c))
                    {
                        logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                        logGW("Unclosed single quote.");
                        return -1;
                    }
                    else
                    {
                        val[v++] = c;
                        val[v]   = '\0';
                    }
                }

                break;

            case 20:

                if (isDoubleQuote(c))
                {
                    val[v] = '\0';
                    s = 5;
                }
                else
                {
                    if (isEOL(c))
                    {
                        logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                        logGW("Unclosed double quote.");
                        return -1;
                    }
                    else if (isREM(c))
                    {
                        logGW("Unexpected character at position %d. Char: 0x%02x '%c'", i, c, c);
                        logGW("Unclosed double quote.");
                        return -1;
                    }
                    else
                    {
                        val[v++] = c;
                        val[v]   = '\0';
                    }
                }

                break;

            default :

                logGW("Invalid state: %d", s);
                logGW("Force exit!");
                exit(-1);

        }

        i++;
    }

    return 1;
}

void readConfigFile(FILE *fd, transport_cfg *cfg)
{
    int i = 0, p;
    char line[CONFIG_MAX_LEN + 1];
    char key[CONFIG_MAX_LEN + 1];
    char val[CONFIG_MAX_LEN + 1];

    // Common
    char cfg_app_dbhost                      = 0;
    char cfg_app_dbname                      = 0;
    char cfg_app_dbuser                      = 0;
    char cfg_app_dbpass                      = 0;
    char cfg_app_dbport                      = 0;
    char cfg_app_prefix                      = 0;

    // LGK
    char cfg_lgk_ip                          = 0;
    char cfg_lgk_port                        = 0;
    char cfg_lgk_lmo_request_uri             = 0;
    char cfg_lgk_ack_request_uri             = 0;
    char cfg_lgk_rmt_request_uri             = 0;
    char cfg_lgk_sp_update_mt                = 0;
    char cfg_lgk_sp_insert_mo                = 0;
    char cfg_lgk_sp_insert_ack               = 0;
    char cfg_lgk_max_srv                     = 0;
    char cfg_lgk_poll_time                   = 0;
    char cfg_lgk_show_wait                   = 0;

    // DGK
    char cfg_dgk_laws_url                    = 0;
    char cfg_dgk_threads_count               = 0;
    char cfg_dgk_sp_status_mt                = 0;
    char cfg_dgk_sp_select_mt                = 0;
    char cfg_dgk_sp_update_mt                = 0;
    char cfg_dgk_inactive_time               = 0;
    char cfg_dgk_read_wait                   = 0;
    char cfg_dgk_show_wait                   = 0;
    char cfg_dgk_batch_size                  = 0;
    char cfg_dgk_batch_rps                   = 0;

    // DMO
    char cfg_dmo_threads_count               = 0;
    char cfg_dmo_sp_status_mo                = 0;
	char cfg_dmo_sp_select_mo                = 0;
	char cfg_dmo_sp_update_mo                = 0;
	char cfg_dmo_user                        = 0;
	char cfg_dmo_pass                        = 0;
    char cfg_dmo_inactive_time               = 0;
	char cfg_dmo_read_wait                   = 0;
	char cfg_dmo_show_wait                   = 0;
	char cfg_dmo_batch_size                  = 0;
	char cfg_dmo_batch_rps                   = 0;
    
    // DACK
    char cfg_dack_threads_count              = 0;
    char cfg_dack_sp_status_ack              = 0;
	char cfg_dack_sp_select_ack              = 0;
	char cfg_dack_sp_update_ack              = 0;
	char cfg_dack_user                       = 0;
	char cfg_dack_pass                       = 0;
    char cfg_dack_inactive_time              = 0;
	char cfg_dack_read_wait                  = 0;
	char cfg_dack_show_wait                  = 0;
	char cfg_dack_batch_size                 = 0;
	char cfg_dack_batch_rps                  = 0;
    
    // MSD
    char cfg_msd_read_wait                   = 0;
    char cfg_msd_show_wait                   = 0;
    char cfg_msd_batch_size                  = 0;
    char cfg_msd_post_time                   = 0;
    char cfg_msd_sp_blacklist                = 0;
    char cfg_msd_sp_start_campaign           = 0;
    char cfg_msd_sp_move_campaign            = 0;
    char cfg_msd_sp_end_campaign             = 0;
    char cfg_msd_sp_post_campaign            = 0;

    while (fgets(line, CONFIG_MAX_LEN + 1, fd) != NULL)
    {
        i++;

        if (strlen(line) >= CONFIG_MAX_LEN)
        {
            logGW("Invalid config data: line %d is too long", i);
            logGW("Force exit!");
            exit(-1);
        }

        p = parseLine(line, key, val);
        if (p == 0)
        {
            // comment or blank line. ignore.
        }
        else if (p == 1)
        {

            // Common
            if (strcasecmp(key, "APP_DBHOST") == 0)
            {
                strncpy(cfg->app_dbhost, val, 100);
                cfg->app_dbhost[100] = '\0';
                cfg_app_dbhost = 1;
            }
            else if (strcasecmp(key, "APP_DBNAME") == 0)
            {
                strncpy(cfg->app_dbname, val, 100);
                cfg->app_dbname[100] = '\0';
                cfg_app_dbname = 1;
            }
            else if (strcasecmp(key, "APP_DBUSER") == 0)
            {
                strncpy(cfg->app_dbuser, val, 100);
                cfg->app_dbuser[100] = '\0';
                cfg_app_dbuser = 1;
            }
            else if (strcasecmp(key, "APP_DBPASS") == 0)
            {
                strncpy(cfg->app_dbpass, val, 100);
                cfg->app_dbpass[100] = '\0';
                cfg_app_dbpass = 1;
            }
            else if (strcasecmp(key, "APP_DBPORT") == 0)
            {
                cfg->app_dbport = atoi(val);
                cfg_app_dbport = 1;
            }
            else if (strcasecmp(key, "APP_PREFIX") == 0)
            {
                strncpy(cfg->app_prefix, val, 20);
                cfg->app_prefix[20] = '\0';
                cfg_app_prefix = 1;
            }

            // LGK
            else if (strcasecmp(key, "LGK_IP") == 0)
            {
                strncpy(cfg->lgk_ip, val, 15);
                cfg->lgk_ip[15] = '\0';
                cfg_lgk_ip = 1;
            }
            else if (strcasecmp(key, "LGK_PORT") == 0)
            {
                cfg->lgk_port = atoi(val);
                cfg_lgk_port = 1;
            }
            else if (strcasecmp(key, "LGK_LMO_REQUEST_URI") == 0)
            {
                strncpy(cfg->lgk_lmo_request_uri, val, 100);
                cfg->lgk_lmo_request_uri[100] = '\0';
                cfg_lgk_lmo_request_uri = 1;
            }
            else if (strcasecmp(key, "LGK_ACK_REQUEST_URI") == 0)
            {
                strncpy(cfg->lgk_ack_request_uri, val, 100);
                cfg->lgk_ack_request_uri[100] = '\0';
                cfg_lgk_ack_request_uri = 1;
            }
            else if (strcasecmp(key, "LGK_RMT_REQUEST_URI") == 0)
            {
                strncpy(cfg->lgk_rmt_request_uri, val, 100);
                cfg->lgk_rmt_request_uri[100] = '\0';
                cfg_lgk_rmt_request_uri = 1;
            }
            else if (strcasecmp(key, "LGK_SP_UPDATE_MT") == 0)
            {
                strncpy(cfg->lgk_sp_update_mt, val, 80);
                cfg->lgk_sp_update_mt[80] = '\0';
                cfg_lgk_sp_update_mt = 1;
            }
            else if (strcasecmp(key, "LGK_SP_INSERT_MO") == 0)
            {
                strncpy(cfg->lgk_sp_insert_mo, val, 80);
                cfg->lgk_sp_insert_mo[80] = '\0';
                cfg_lgk_sp_insert_mo = 1;
            }
            else if (strcasecmp(key, "LGK_SP_INSERT_ACK") == 0)
            {
                strncpy(cfg->lgk_sp_insert_ack, val, 80);
                cfg->lgk_sp_insert_ack[80] = '\0';
                cfg_lgk_sp_insert_ack = 1;
            }
            else if (strcasecmp(key, "LGK_MAX_SRV") == 0)
            {
                cfg->lgk_max_srv = atoi(val);
                cfg_lgk_max_srv = 1;
            }
            else if (strcasecmp(key, "LGK_POLL_TIME") == 0)
            {
                cfg->lgk_poll_time = atoi(val);
                cfg_lgk_poll_time = 1;
            }
            else if (strcasecmp(key, "LGK_SHOW_WAIT") == 0)
            {
                cfg->lgk_show_wait = atoi(val);
                cfg_lgk_show_wait = 1;
            }

            // DGK
            else if (strcasecmp(key, "DGK_LAWS_URL") == 0)
            {
                strncpy(cfg->dgk_laws_url, val, 255);
                cfg->dgk_laws_url[255] = '\0';
                cfg_dgk_laws_url = 1;
            }
            else if (strcasecmp(key, "DGK_THREADS_COUNT") == 0)
            {
                cfg->dgk_threads_count = atoi(val);
                cfg_dgk_threads_count = 1;
            }
            else if (strcasecmp(key, "DGK_SP_STATUS_MT") == 0)
            {
                strncpy(cfg->dgk_sp_status_mt, val, 80);
                cfg->dgk_sp_status_mt[80] = '\0';
                cfg_dgk_sp_status_mt = 1;
            }
            else if (strcasecmp(key, "DGK_SP_SELECT_MT") == 0)
            {
                strncpy(cfg->dgk_sp_select_mt, val, 80);
                cfg->dgk_sp_select_mt[80] = '\0';
                cfg_dgk_sp_select_mt = 1;
            }
            else if (strcasecmp(key, "DGK_SP_UPDATE_MT") == 0)
            {
                strncpy(cfg->dgk_sp_update_mt, val, 80);
                cfg->dgk_sp_update_mt[80] = '\0';
                cfg_dgk_sp_update_mt = 1;
            }
            else if (strcasecmp(key, "DGK_INACTIVE_TIME") == 0)
            {
                cfg->dgk_inactive_time = atoi(val);
                cfg_dgk_inactive_time = 1;
            }
            else if (strcasecmp(key, "DGK_READ_WAIT") == 0)
            {
                cfg->dgk_read_wait = atoi(val);
                cfg_dgk_read_wait = 1;
            }
            else if (strcasecmp(key, "DGK_SHOW_WAIT") == 0)
            {
                cfg->dgk_show_wait = atoi(val);
                cfg_dgk_show_wait = 1;
            }
            else if (strcasecmp(key, "DGK_BATCH_SIZE") == 0)
            {
                cfg->dgk_batch_size = atoi(val);
                cfg_dgk_batch_size = 1;
            }
            else if (strcasecmp(key, "DGK_BATCH_RPS") == 0)
            {
                cfg->dgk_batch_rps = atoi(val);
                cfg_dgk_batch_rps = 1;
            }

            // DMO
            else if (strcasecmp(key, "DMO_THREADS_COUNT") == 0)
            {
                cfg->dmo_threads_count = atoi(val);
                cfg_dmo_threads_count = 1;
            }
            else if (strcasecmp(key, "DMO_SP_STATUS_MO") == 0)
            {
                strncpy(cfg->dmo_sp_status_mo, val, 80);
                cfg->dmo_sp_status_mo[80] = '\0';
                cfg_dmo_sp_status_mo = 1;
            }
            else if (strcasecmp(key, "DMO_SP_SELECT_MO") == 0)
            {
                strncpy(cfg->dmo_sp_select_mo, val, 80);
                cfg->dmo_sp_select_mo[80] = '\0';
                cfg_dmo_sp_select_mo = 1;
            }
            else if (strcasecmp(key, "DMO_SP_UPDATE_MO") == 0)
            {
                strncpy(cfg->dmo_sp_update_mo, val, 80);
                cfg->dmo_sp_update_mo[80] = '\0';
                cfg_dmo_sp_update_mo = 1;
            }
            else if (strcasecmp(key, "DMO_USER") == 0)
            {
                strncpy(cfg->dmo_user, val, 80);
                cfg->dmo_user[80] = '\0';
                cfg_dmo_user = 1;
            }
            else if (strcasecmp(key, "DMO_PASS") == 0)
            {
                strncpy(cfg->dmo_pass, val, 80);
                cfg->dmo_pass[80] = '\0';
                cfg_dmo_pass = 1;
            }
            else if (strcasecmp(key, "DMO_INACTIVE_TIME") == 0)
            {
                cfg->dmo_inactive_time = atoi(val);
                cfg_dmo_inactive_time = 1;
            }
            else if (strcasecmp(key, "DMO_READ_WAIT") == 0)
            {
                cfg->dmo_read_wait = atoi(val);
                cfg_dmo_read_wait = 1;
            }
            else if (strcasecmp(key, "DMO_SHOW_WAIT") == 0)
            {
                cfg->dmo_show_wait = atoi(val);
                cfg_dmo_show_wait = 1;
            }
            else if (strcasecmp(key, "DMO_BATCH_SIZE") == 0)
            {
                cfg->dmo_batch_size = atoi(val);
                cfg_dmo_batch_size = 1;
            }
            else if (strcasecmp(key, "DMO_BATCH_RPS") == 0)
            {
                cfg->dmo_batch_rps = atoi(val);
                cfg_dmo_batch_rps = 1;
            }

            // DACK
            else if (strcasecmp(key, "DACK_THREADS_COUNT") == 0)
            {
                cfg->dack_threads_count = atoi(val);
                cfg_dack_threads_count = 1;
            }
            else if (strcasecmp(key, "DACK_SP_STATUS_ACK") == 0)
            {
                strncpy(cfg->dack_sp_status_ack, val, 80);
                cfg->dack_sp_status_ack[80] = '\0';
                cfg_dack_sp_status_ack = 1;
            }
            else if (strcasecmp(key, "DACK_SP_SELECT_ACK") == 0)
            {
                strncpy(cfg->dack_sp_select_ack, val, 80);
                cfg->dack_sp_select_ack[80] = '\0';
                cfg_dack_sp_select_ack = 1;
            }
            else if (strcasecmp(key, "DACK_SP_UPDATE_ACK") == 0)
            {
                strncpy(cfg->dack_sp_update_ack, val, 80);
                cfg->dack_sp_update_ack[80] = '\0';
                cfg_dack_sp_update_ack = 1;
            }
            else if (strcasecmp(key, "DACK_USER") == 0)
            {
                strncpy(cfg->dack_user, val, 80);
                cfg->dack_user[80] = '\0';
                cfg_dack_user = 1;
            }
            else if (strcasecmp(key, "DACK_PASS") == 0)
            {
                strncpy(cfg->dack_pass, val, 80);
                cfg->dack_pass[80] = '\0';
                cfg_dack_pass = 1;
            }
            else if (strcasecmp(key, "DACK_INACTIVE_TIME") == 0)
            {
                cfg->dack_inactive_time = atoi(val);
                cfg_dack_inactive_time = 1;
            }
            else if (strcasecmp(key, "DACK_READ_WAIT") == 0)
            {
                cfg->dack_read_wait = atoi(val);
                cfg_dack_read_wait = 1;
            }
            else if (strcasecmp(key, "DACK_SHOW_WAIT") == 0)
            {
                cfg->dack_show_wait = atoi(val);
                cfg_dack_show_wait = 1;
            }
            else if (strcasecmp(key, "DACK_BATCH_SIZE") == 0)
            {
                cfg->dack_batch_size = atoi(val);
                cfg_dack_batch_size = 1;
            }
            else if (strcasecmp(key, "DACK_BATCH_RPS") == 0)
            {
                cfg->dack_batch_rps = atoi(val);
                cfg_dack_batch_rps = 1;
            }

            // MSD
            else if (strcasecmp(key, "MSD_READ_WAIT") == 0)
            {
                cfg->msd_read_wait = atoi(val);
                cfg_msd_read_wait = 1;
            }
            else if (strcasecmp(key, "MSD_SHOW_WAIT") == 0)
            {
                cfg->msd_show_wait = atoi(val);
                cfg_msd_show_wait = 1;
            }
            else if (strcasecmp(key, "MSD_BATCH_SIZE") == 0)
            {
                cfg->msd_batch_size = atoi(val);
                cfg_msd_batch_size = 1;
            }
            else if (strcasecmp(key, "MSD_POST_TIME") == 0)
            {
                cfg->msd_post_time = atoi(val);
                cfg_msd_post_time = 1;
            }
            else if (strcasecmp(key, "MSD_SP_BLACKLIST") == 0)
            {
                strncpy(cfg->msd_sp_blacklist, val, 80);
                cfg->msd_sp_blacklist[80] = '\0';
                cfg_msd_sp_blacklist = 1;
            }
            else if (strcasecmp(key, "MSD_SP_START_CAMPAIGN") == 0)
            {
                strncpy(cfg->msd_sp_start_campaign, val, 80);
                cfg->msd_sp_start_campaign[80] = '\0';
                cfg_msd_sp_start_campaign = 1;
            }
            else if (strcasecmp(key, "MSD_SP_MOVE_CAMPAIGN") == 0)
            {
                strncpy(cfg->msd_sp_move_campaign, val, 80);
                cfg->msd_sp_move_campaign[80] = '\0';
                cfg_msd_sp_move_campaign = 1;
            }
            else if (strcasecmp(key, "MSD_SP_END_CAMPAIGN") == 0)
            {
                strncpy(cfg->msd_sp_end_campaign, val, 80);
                cfg->msd_sp_end_campaign[80] = '\0';
                cfg_msd_sp_end_campaign = 1;
            }
            else if (strcasecmp(key, "MSD_SP_POST_CAMPAIGN") == 0)
            {
                strncpy(cfg->msd_sp_post_campaign, val, 80);
                cfg->msd_sp_post_campaign[80] = '\0';
                cfg_msd_sp_post_campaign = 1;
            }

            // Not defined
            else
            {
                logGW("Ignored configutarion entry for line %d: '%s' => '%s'", i, key, val);
            }

        }
        else
        {
            logGW("Invalid config data: parse error at line %d.", i);
            logGW("Force exit!");
            exit(-1);
        }

    }

    // Common

    if (! cfg_app_dbhost)
    {
        logGW("Missing configuration value for 'APP_DBHOST'. Force exit!");
        exit(-1);
    }

    if (! cfg_app_dbname)
    {
        logGW("Missing configuration value for 'APP_DBNAME'. Force exit!");
        exit(-1);
    }

    if (! cfg_app_dbuser)
    {
        logGW("Missing configuration value for 'APP_DBUSER'. Force exit!");
        exit(-1);
    }

    if (! cfg_app_dbpass)
    {
        logGW("Missing configuration value for 'APP_DBPASS'. Force exit!");
        exit(-1);
    }

    if (! cfg_app_dbport)
    {
        logGW("Missing configuration value for 'APP_DBPORT'. Force exit!");
        exit(-1);
    }

    if (! cfg_app_prefix)
    {
        logGW("Missing configuration value for 'APP_PREFIX'. Force exit!");
        exit(-1);
    }

    // LGK

    if (! cfg_lgk_ip)
    {
        logGW("Missing configuration value for 'LGK_IP'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_port)
    {
        logGW("Missing configuration value for 'LGK_PORT'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_lmo_request_uri)
    {
        logGW("Missing configuration value for 'LGK_LMO_REQUEST_URI'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_ack_request_uri)
    {
        logGW("Missing configuration value for 'LGK_ACK_REQUEST_URI'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_rmt_request_uri)
    {
        logGW("Missing configuration value for 'LGK_RMT_REQUEST_URI'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_sp_update_mt)
    {
        logGW("Missing configuration value for 'LGK_SP_UPDATE_MT'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_sp_insert_mo)
    {
        logGW("Missing configuration value for 'LGK_SP_INSERT_MO'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_sp_insert_ack)
    {
        logGW("Missing configuration value for 'LGK_SP_INSERT_ACK'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_max_srv)
    {
        logGW("Missing configuration value for 'LGK_MAX_SRV'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_poll_time)
    {
        logGW("Missing configuration value for 'LGK_POLL_TIME'. Force exit!");
        exit(-1);
    }

    if (! cfg_lgk_show_wait)
    {
        logGW("Missing configuration value for 'LGK_SHOW_WAIT'. Force exit!");
        exit(-1);
    }


    // DGK

    if (! cfg_dgk_laws_url)
    {
        logGW("Missing configuration value for 'DGK_LAWS_URL'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_threads_count)
    {
        logGW("Missing configuration value for 'DGK_THREADS_COUNT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_sp_status_mt)
    {
        logGW("Missing configuration value for 'DGK_SP_STATUS_MT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_sp_select_mt)
    {
        logGW("Missing configuration value for 'DGK_SP_SELECT_MT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_sp_update_mt)
    {
        logGW("Missing configuration value for 'DGK_SP_UPDATE_MT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_inactive_time)
    {
        logGW("Missing configuration value for 'DGK_INACTIVE_TIME'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_read_wait)
    {
        logGW("Missing configuration value for 'DGK_READ_WAIT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_show_wait)
    {
        logGW("Missing configuration value for 'DGK_SHOW_WAIT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_batch_size)
    {
        logGW("Missing configuration value for 'DGK_BATCH_SIZE'. Force exit!");
        exit(-1);
    }

    if (! cfg_dgk_batch_rps)
    {
        logGW("Missing configuration value for 'DGK_BATCH_RPS'. Force exit!");
        exit(-1);
    }

    // DMO

    if (! cfg_dmo_threads_count)
    {
        logGW("Missing configuration value for 'DMO_THREADS_COUNT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_sp_status_mo)
    {
        logGW("Missing configuration value for 'DMO_SP_STATUS_MO'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_sp_select_mo)
    {
        logGW("Missing configuration value for 'DMO_SP_SELECT_MO'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_sp_update_mo)
    {
        logGW("Missing configuration value for 'DMO_SP_UPDATE_MO'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_user)
    {
        logGW("Missing configuration value for 'DMO_USER'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_pass)
    {
        logGW("Missing configuration value for 'DMO_PASS'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_inactive_time)
    {
        logGW("Missing configuration value for 'DMO_INACTIVE_TIME'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_read_wait)
    {
        logGW("Missing configuration value for 'DMO_READ_WAIT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_show_wait)
    {
        logGW("Missing configuration value for 'DMO_SHOW_WAIT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_batch_size)
    {
        logGW("Missing configuration value for 'DMO_BATCH_SIZE'. Force exit!");
        exit(-1);
    }

    if (! cfg_dmo_batch_rps)
    {
        logGW("Missing configuration value for 'DMO_BATCH_RPS'. Force exit!");
        exit(-1);
    }


    // DACK

    if (! cfg_dack_threads_count)
    {
        logGW("Missing configuration value for 'DACK_THREADS_COUNT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_sp_status_ack)
    {
        logGW("Missing configuration value for 'DACK_SP_STATUS_ACK'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_sp_select_ack)
    {
        logGW("Missing configuration value for 'DACK_SP_SELECT_ACK'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_sp_update_ack)
    {
        logGW("Missing configuration value for 'DACK_SP_UPDATE_ACK'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_user)
    {
        logGW("Missing configuration value for 'DACK_USER'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_pass)
    {
        logGW("Missing configuration value for 'DACK_PASS'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_inactive_time)
    {
        logGW("Missing configuration value for 'DACK_INACTIVE_TIME'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_read_wait)
    {
        logGW("Missing configuration value for 'DACK_READ_WAIT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_show_wait)
    {
        logGW("Missing configuration value for 'DACK_SHOW_WAIT'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_batch_size)
    {
        logGW("Missing configuration value for 'DACK_BATCH_SIZE'. Force exit!");
        exit(-1);
    }

    if (! cfg_dack_batch_rps)
    {
        logGW("Missing configuration value for 'DACK_BATCH_RPS'. Force exit!");
        exit(-1);
    }


    // MSD

    if (! cfg_msd_read_wait)
    {
        logGW("Missing configuration value for 'MSD_READ_WAIT'. Force exit!");
        exit(-1);
    }

    if (! cfg_msd_show_wait)
    {
        logGW("Missing configuration value for 'MSD_SHOW_WAIT'. Force exit!");
        exit(-1);
    }

    if (! cfg_msd_batch_size)
    {
        logGW("Missing configuration value for 'MSD_BATCH_SIZE'. Force exit!");
        exit(-1);
    }

    if (! cfg_msd_post_time)
    {
        logGW("Missing configuration value for 'MSD_POST_TIME'. Force exit!");
        exit(-1);
    }

    if (! cfg_msd_sp_blacklist)
    {
        logGW("Missing configuration value for 'MSD_SP_BLACKLIST'. Force exit!");
        exit(-1);
    }

    if (! cfg_msd_sp_start_campaign)
    {
        logGW("Missing configuration value for 'MSD_SP_START_CAMPAIGN'. Force exit!");
        exit(-1);
    }

    if (! cfg_msd_sp_move_campaign)
    {
        logGW("Missing configuration value for 'MSD_SP_MOVE_CAMPAIGN'. Force exit!");
        exit(-1);
    }

    if (! cfg_msd_sp_end_campaign)
    {
        logGW("Missing configuration value for 'MSD_SP_END_CAMPAIGN'. Force exit!");
        exit(-1);
    }

    if (! cfg_msd_sp_post_campaign)
    {
        logGW("Missing configuration value for 'MSD_SP_POST_CAMPAIGN'. Force exit!");
        exit(-1);
    }

    return;
}

void showConfig(transport_cfg *cfg)
{
    logGW("--------------------------- Config -------------------------");
    logGW("  APP_DBHOST                       : ->%s<-", cfg->app_dbhost);
    logGW("  APP_DBNAME                       : ->%s<-", cfg->app_dbname);
    logGW("  APP_DBUSER                       : ->%s<-", cfg->app_dbuser);
    logGW("  APP_DBPASS                       : ->%s<-", cfg->app_dbpass);
    logGW("  APP_DBPORT                       : ->%u<-", cfg->app_dbport);
    logGW("  APP_PREFIX                       : ->%s<-", cfg->app_prefix);
    logGW("");
    logGW("  LGK_IP                           : ->%s<-", cfg->lgk_ip);
    logGW("  LGK_PORT                         : ->%d<-", cfg->lgk_port);
    logGW("  LGK_LMO_REQUEST_URI              : ->%s<-", cfg->lgk_lmo_request_uri);
    logGW("  LGK_ACK_REQUEST_URI              : ->%s<-", cfg->lgk_ack_request_uri);
    logGW("  LGK_RMT_REQUEST_URI              : ->%s<-", cfg->lgk_rmt_request_uri);
    logGW("  LGK_SP_UPDATE_MT                 : ->%s<-", cfg->lgk_sp_update_mt);
    logGW("  LGK_SP_INSERT_MO                 : ->%s<-", cfg->lgk_sp_insert_mo);
    logGW("  LGK_SP_INSERT_ACK                : ->%s<-", cfg->lgk_sp_insert_ack);
    logGW("  LGK_MAX_SRV                      : ->%d<-", cfg->lgk_max_srv);
    logGW("  LGK_POLL_TIME                    : ->%d<-", cfg->lgk_poll_time);
    logGW("  LGK_SHOW_WAIT                    : ->%d<-", cfg->lgk_show_wait);
    logGW("");
    logGW("  DGK_LAWS_URL                     : ->%s<-", cfg->dgk_laws_url);
    logGW("  DGK_THREADS_COUNT                : ->%d<-", cfg->dgk_threads_count);
    logGW("  DGK_SP_STATUS_MT                 : ->%s<-", cfg->dgk_sp_status_mt);
    logGW("  DGK_SP_SELECT_MT                 : ->%s<-", cfg->dgk_sp_select_mt);
    logGW("  DGK_SP_UPDATE_MT                 : ->%s<-", cfg->dgk_sp_update_mt);
    logGW("  DGK_INACTIVE_TIME                : ->%d<-", cfg->dgk_inactive_time);
    logGW("  DGK_READ_WAIT                    : ->%d<-", cfg->dgk_read_wait);
    logGW("  DGK_SHOW_WAIT                    : ->%d<-", cfg->dgk_show_wait);
    logGW("  DGK_BATCH_SIZE                   : ->%d<-", cfg->dgk_batch_size);
    logGW("  DGK_BATCH_RPS                    : ->%d<-", cfg->dgk_batch_rps);
    logGW("");
    logGW("  DMO_THREADS_COUNT                : ->%d<-", cfg->dmo_threads_count);
    logGW("  DMO_SP_STATUS_MO                 : ->%s<-", cfg->dmo_sp_status_mo);
    logGW("  DMO_SP_SELECT_MO                 : ->%s<-", cfg->dmo_sp_select_mo);
    logGW("  DMO_SP_UPDATE_MO                 : ->%s<-", cfg->dmo_sp_update_mo);
    logGW("  DMO_USER                         : ->%s<-", cfg->dmo_user);
    logGW("  DMO_PASS                         : ->%s<-", cfg->dmo_pass);
    logGW("  DMO_INACTIVE_TIME                : ->%d<-", cfg->dmo_inactive_time);
    logGW("  DMO_READ_WAIT                    : ->%d<-", cfg->dmo_read_wait);
    logGW("  DMO_SHOW_WAIT                    : ->%d<-", cfg->dmo_show_wait);
    logGW("  DMO_BATCH_SIZE                   : ->%d<-", cfg->dmo_batch_size);
    logGW("  DMO_BATCH_RPS                    : ->%d<-", cfg->dmo_batch_rps);
    logGW("");
    logGW("  DACK_THREADS_COUNT               : ->%d<-", cfg->dack_threads_count);
    logGW("  DACK_SP_STATUS_ACK               : ->%s<-", cfg->dack_sp_status_ack);
    logGW("  DACK_SP_SELECT_ACK               : ->%s<-", cfg->dack_sp_select_ack);
    logGW("  DACK_SP_UPDATE_ACK               : ->%s<-", cfg->dack_sp_update_ack);
    logGW("  DACK_USER                        : ->%s<-", cfg->dack_user);
    logGW("  DACK_PASS                        : ->%s<-", cfg->dack_pass);
    logGW("  DACK_INACTIVE_TIME               : ->%d<-", cfg->dack_inactive_time);
    logGW("  DACK_READ_WAIT                   : ->%d<-", cfg->dack_read_wait);
    logGW("  DACK_SHOW_WAIT                   : ->%d<-", cfg->dack_show_wait);
    logGW("  DACK_BATCH_SIZE                  : ->%d<-", cfg->dack_batch_size);
    logGW("  DACK_BATCH_RPS                   : ->%d<-", cfg->dack_batch_rps);
    logGW("");
    logGW("  MSD_READ_WAIT                    : ->%d<-", cfg->msd_read_wait);
    logGW("  MSD_SHOW_WAIT                    : ->%d<-", cfg->msd_show_wait);
    logGW("  MSD_BATCH_SIZE                   : ->%d<-", cfg->msd_batch_size);
    logGW("  MSD_POST_TIME                    : ->%d<-", cfg->msd_post_time);
    logGW("  MSD_SP_BLACKLIST                 : ->%s<-", cfg->msd_sp_blacklist);
    logGW("  MSD_SP_START_CAMPAIGN            : ->%s<-", cfg->msd_sp_start_campaign);
    logGW("  MSD_SP_MOVE_CAMPAIGN             : ->%s<-", cfg->msd_sp_move_campaign);
    logGW("  MSD_SP_END_CAMPAIGN              : ->%s<-", cfg->msd_sp_end_campaign);
    logGW("  MSD_SP_POST_CAMPAIGN             : ->%s<-", cfg->msd_sp_post_campaign);
    logGW("------------------------------------------------------------");
}

/*************************************************/

void openMySQL(MYSQL *con, char *dbhost, char *dbname, char *dbuser, char *dbpass, unsigned int dbport)
{
    char info[2048 + 1];
    info[2048] = '\0';

    if ( ! mysql_init(con) )
    {
		logGW("dbaccess> Failed to init connection: No memory available.");
		logGW("Force exit !");
        traceExit(-1, "dbaccess> Failed to init connection: No memory available.");
    }

    my_bool reconnect = 1;
    mysql_options(con, MYSQL_OPT_RECONNECT, &reconnect);
    mysql_options(con, MYSQL_SET_CHARSET_NAME, "utf8");

    if ( ! mysql_real_connect(con, dbhost, dbuser, dbpass, NULL, dbport, NULL, 0) )
    {
        snprintf(info, 2048, "dbaccess> Failed to connect to database at port %u: Error: %s", dbport, mysql_error(con));
		logGW("%s", info);
		logGW("Force exit !");
        traceExit(-1, info);
    }

    if (mysql_select_db(con, dbname))
    {
        snprintf(info, 2048, "dbaccess> Failed to select database: Error: %s.", mysql_error(con));
		logGW("%s", info);
		logGW("Force exit !");
        traceExit(-1, info);
    }

    return;
}

void closeMySQL(MYSQL *con)
{
    mysql_close(con);
}

void quote(MYSQL *con, char *out_str, char *in_str)
{
    if (in_str == NULL)
    {
        strcpy(out_str, "NULL");
    }
    else
    {
        char aux[1024 + 1];
        mysql_real_escape_string(con, aux, in_str, strlen(in_str));
        sprintf(out_str, "'%s'", aux);
    }
}

int dbStatusDMT(MYSQL *con, statusQDMT *statusDMT, char *store_procedure)
{
    char   query_str[1024];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;
    int j;

    sprintf(query_str, "CALL %s(%d)", store_procedure, MAX_COMPANIES);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbStatusDMT> Failed to select MT status for dispatch: Error: %s", mysql_error(con));
        logGW("dbStatusDMT> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbStatusDMT> Failed to select MT status for dispatch.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbStatusDMT> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbStatusDMT> Failed to retrieve the result set.");
    }

    j = 0;
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {
        statusDMT[j].company          = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(statusDMT[j].gkv_token , (row[1] == NULL) ? ""     : row[1]);
        statusDMT[j].count            = (row[2] == NULL) ? 0 : atoi(row[2]);
        j++;
    }

    if (j >= (int)(MAX_COMPANIES * 0.8))
    {
		logGW("********************************************************");
		logGW("WARNING: 80%% of MAX Companies has been reached.");
		logGW("WARNING: Current companies %d. Max companies %d.", j, MAX_COMPANIES);
		logGW("WARNING: This limit must be reconfigured and programs recompiled.");
		logGW("********************************************************");
	}
	
    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return j;
}

int dbSelectDMT(MYSQL *con, msgQDMT *queueSMS, int company, int queue, int queue_size, int limit, char *store_procedure)
{
    char   query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;
    int j;

    sprintf(query_str, "CALL %s(%d, %d, %d, %d)", store_procedure, company, queue, queue_size, limit);
    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbSelectDMT> Failed to select MT messages for dispatch: Error: %s", mysql_error(con));
        logGW("dbSelectDMT> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbSelectDMT> Failed to select MT messages for dispatch.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbSelectDMT> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbSelectDMT> Failed to retrieve the result set.");
    }

    j = 0;
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {
        queueSMS[j].id                   = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(queueSMS[j].recipientId   , (row[1] == NULL) ? ""     : row[1]);
        strcpy(queueSMS[j].msgText       , (row[2] == NULL) ? ""     : row[2]);
        strcpy(queueSMS[j].cc            , (row[3] == NULL) ? ""     : row[3]);

        j++;
    }

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return j;
}

int dbUpdateDMT(MYSQL *con, int id, char *status, char *operator, int errCode, char *errText, char *gkv_id, char *store_procedure)
{
    char query_str[2048];

    char field_status[2 * 20 + 1];
    char field_operator[2 * 30 + 1];
    char field_errText[2 * 255 + 1];
    char field_gkv_id[2 * 45 + 1];

    quote(con, field_status, status);
    quote(con, field_operator, operator);
    quote(con, field_errText, errText);
    quote(con, field_gkv_id, gkv_id);

    sprintf(query_str, "CALL %s(%d, %s, %s, %d, %s, %s)", store_procedure, id, field_status, field_operator, errCode, field_errText, field_gkv_id);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbUpdateDMT> Failed to update DMT message: Error: %s", mysql_error(con));
        logGW("dbUpdateDMT> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbUpdateDMT> Failed to update DMT message.");
    }

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return 0;
}

int dbStatusDMO(MYSQL *con, statusQDMO *statusDMO, char *store_procedure)
{
    char   query_str[1024];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;
    int j;

    sprintf(query_str, "CALL %s(%d)", store_procedure, MAX_COMPANIES);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbStatusDMO> Failed to select MO status for dispatch: Error: %s", mysql_error(con));
        logGW("dbStatusDMO> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbStatusDMO> Failed to select MO status for dispatch.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbStatusDMO> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbStatusDMO> Failed to retrieve the result set.");
    }

    j = 0;
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {
        statusDMO[j].company               = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(statusDMO[j].url_adapter_mo , (row[1] == NULL) ? ""     : row[1]);
        statusDMO[j].count                 = (row[2] == NULL) ? 0 : atoi(row[2]);
        j++;
    }

    if (j >= (int)(MAX_COMPANIES * 0.8))
    {
		logGW("********************************************************");
		logGW("WARNING: 80%% of MAX Companies has been reached.");
		logGW("WARNING: Current companies %d. Max companies %d.", j, MAX_COMPANIES);
		logGW("WARNING: This limit must be reconfigured and programs recompiled.");
		logGW("********************************************************");
	}
	
    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return j;
}

int dbSelectDMO(MYSQL *con, msgQDMO *queueSMS, int company, int queue, int queue_size, int limit, char *store_procedure)
{
    char   query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;
    int j;

    sprintf(query_str, "CALL %s(%d, %d, %d, %d)", store_procedure, company, queue, queue_size, limit);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbSelectDMO> Failed to select MO messages for dispatch: Error: %s", mysql_error(con));
        logGW("dbSelectDMO> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbSelectDMO> Failed to select MO messages for dispatch.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbSelectDMO> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbSelectDMO> Failed to retrieve the result set.");
    }

    j = 0;
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {
        queueSMS[j].id              = (row[0] == NULL) ? 0 : atoi(row[0]);
        queueSMS[j].id_company      = (row[1] == NULL) ? 0 : atoi(row[1]);
        strcpy(queueSMS[j].carrier  , (row[2] == NULL) ? ""     : row[2]);
        strcpy(queueSMS[j].phone    , (row[3] == NULL) ? ""     : row[3]);
        strcpy(queueSMS[j].msgtext  , (row[4] == NULL) ? ""     : row[4]);
        queueSMS[j].delivery_count  = (row[5] == NULL) ? 0 : atoi(row[5]);
        strcpy(queueSMS[j].status   , (row[6] == NULL) ? ""     : row[6]);
        queueSMS[j].time_retry      = (row[7] == NULL) ? 0 : atoi(row[7]);
        queueSMS[j].max_retry       = (row[8] == NULL) ? 0 : atoi(row[8]);

        j++;
    }

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return j;
}

int dbUpdateDMO(MYSQL *con, int id, char *status, int delta, int errCode, char *errText, char *store_procedure)
{
    char query_str[2048];

    char field_status[2 * 30 + 1];
    char field_errText[2 * 255 + 1];

    quote(con, field_status, status);
    quote(con, field_errText, errText);

    sprintf(query_str, "CALL %s(%d, %s, %d, %d, %s)", store_procedure, id, field_status, delta, errCode, field_errText);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbUpdateDMO> Failed to update DMO message: Error: %s", mysql_error(con));
        logGW("dbUpdateDMO> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbUpdateDMO> Failed to update DMO message.");
    }

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return 0;
}

int dbStatusDACK(MYSQL *con, statusQDACK *statusDACK, char *store_procedure)
{
    char   query_str[1024];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;
    int j;

    sprintf(query_str, "CALL %s(%d)", store_procedure, MAX_COMPANIES);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbStatusDACK> Failed to select ACK status for dispatch: Error: %s", mysql_error(con));
        logGW("dbStatusDACK> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbStatusDACK> Failed to select ACK status for dispatch.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbStatusDACK> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbStatusDACK> Failed to retrieve the result set.");
    }

    j = 0;
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {
        statusDACK[j].company                = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(statusDACK[j].url_adapter_ack , (row[1] == NULL) ? ""     : row[1]);
        statusDACK[j].count                  = (row[2] == NULL) ? 0 : atoi(row[2]);
        j++;
    }

    if (j >= (int)(MAX_COMPANIES * 0.8))
    {
		logGW("********************************************************");
		logGW("WARNING: 80%% of MAX Companies has been reached.");
		logGW("WARNING: Current companies %d. Max companies %d.", j, MAX_COMPANIES);
		logGW("WARNING: This limit must be reconfigured and programs recompiled.");
		logGW("********************************************************");
	}
	
    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return j;
}

int dbSelectDACK(MYSQL *con, msgQDACK *queueSMS, int company, int queue, int queue_size, int limit, char *store_procedure)
{
    char   query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;
    int j;

    sprintf(query_str, "CALL %s(%d, %d, %d, %d)", store_procedure, company, queue, queue_size, limit);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbSelectDACK> Failed to select ACK messages for dispatch: Error: %s", mysql_error(con));
        logGW("dbSelectDACK> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbSelectDACK> Failed to select ACK messages for dispatch.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbSelectDACK> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbSelectDACK> Failed to retrieve the result set.");
    }

    j = 0;
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {

        queueSMS[j].id                   = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(queueSMS[j].clientCode    , (row[1] == NULL) ? ""     : row[1]);
        strcpy(queueSMS[j].mobileNumber  , (row[2] == NULL) ? ""     : row[2]);
        strcpy(queueSMS[j].status        , (row[3] == NULL) ? ""     : row[3]);
        strcpy(queueSMS[j].message       , (row[4] == NULL) ? ""     : row[4]);
        strcpy(queueSMS[j].carrier       , (row[5] == NULL) ? ""     : row[5]);
        strcpy(queueSMS[j].date          , (row[6] == NULL) ? ""     : row[6]);
        queueSMS[j].delivery_count       = (row[7] == NULL) ? 0 : atoi(row[7]);
        queueSMS[j].time_retry           = (row[8] == NULL) ? 0 : atoi(row[8]);
        queueSMS[j].max_retry            = (row[9] == NULL) ? 0 : atoi(row[9]);

        j++;
    }

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return j;
}

int dbUpdateDACK(MYSQL *con, int id, char *status, int delta, int errCode, char *errText, char *store_procedure)
{
    char query_str[2048];

    char field_status[2 * 30 + 1];
    char field_errText[2 * 255 + 1];

    quote(con, field_status, status);
    quote(con, field_errText, errText);

    sprintf(query_str, "CALL %s(%d, %s, %d, %d, %s)", store_procedure, id, field_status, delta, errCode, field_errText);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbUpdateDACK> Failed to update DACK message: Error: %s", mysql_error(con));
        logGW("dbUpdateDACK> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbUpdateDACK> Failed to update DACK message.");
    }

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return 0;
}

int dbUpdateLGK(MYSQL *con, int id, char *status, char *operator, int errCode, char *errText, char *gkv_id, char *store_procedure)
{
    char query_str[2048];
 
    char field_status[2 * 20 + 1];
    char field_operator[2 * 30 + 1];
    char field_errText[2 * 255 + 1];
    char field_gkv_id[2 * 45 + 1];

    quote(con, field_status, status);
    quote(con, field_operator, operator);
    quote(con, field_errText, errText);
    quote(con, field_gkv_id, gkv_id);

    sprintf(query_str, "CALL %s(%d, %s, %s, %d, %s, %s)", store_procedure, id, field_status, field_operator, errCode, field_errText, field_gkv_id);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbUpdateLGK> Failed to update LGK message: Error: %s", mysql_error(con));
        logGW("dbUpdateLGK> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbUpdateLGK> Failed to update LGK message.");
    }

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return 0;
}

int dbInsertLGK_MO(MYSQL *con, char *gkv_token, char *operator, char *msisdn, char *message, char *store_procedure)
{
    char   query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;

    char field_gkv_token[2 * 45 + 1];
    char field_operator[2 * 30 + 1];
    char field_msisdn[2 * 30 + 1];
    char field_message[2 * 255 + 1];

    quote(con, field_gkv_token, gkv_token);
    quote(con, field_operator, operator);
    quote(con, field_msisdn, msisdn);
    quote(con, field_message, message);

    sprintf(query_str, "CALL %s(%s, %s, %s, %s)", store_procedure, field_gkv_token, field_operator, field_msisdn, field_message);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbInsertLGK_MO> Failed to insert MO message: Error: %s", mysql_error(con));
        logGW("dbInsertLGK_MO> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbInsertLGK_MO> Failed to insert MO message.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbInsertLGK_MO> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbInsertLGK_MO> Failed to retrieve the result set.");
    }

    int result = 0;
    
    if ((row = mysql_fetch_row(res_set)) != NULL)
    {
        result = (row[0] == NULL) ? 0 : atoi(row[0]);
    }

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return result;
}

int dbInsertLGK_ACK(MYSQL *con, char *app_id, char *msg_id, char *id_gkv, int ref_mt_id, char *phone, char *short_number, char *carrier, char *ack_status, char *ack_reason, char *store_procedure)
{
    char   query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;

    char field_app_id[2 * 45 + 1];
    char field_msg_id[2 * 45 + 1];
    char field_id_gkv[2 * 11 + 1];
    char field_phone[2 * 30 + 1];
    char field_short_number[2 * 30 + 1];
    char field_carrier[2 * 45 + 1];
    char field_ack_status[2 * 20 + 1];
    char field_ack_reason[2 * 255 + 1];

    quote(con, field_app_id, app_id);
    quote(con, field_msg_id, msg_id);
    quote(con, field_id_gkv, id_gkv);
    quote(con, field_phone, phone);
    quote(con, field_short_number, short_number);
    quote(con, field_carrier, carrier);
    quote(con, field_ack_status, ack_status);
    quote(con, field_ack_reason, ack_reason);

    sprintf(query_str, "CALL %s(%s, %s, %s, %d, %s, %s, %s, %s, %s)", store_procedure, field_app_id, field_msg_id, field_id_gkv, ref_mt_id, field_phone, field_short_number, field_carrier, field_ack_status, field_ack_reason);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbInsertLGK_ACK> Failed to insert ACK message: Error: %s", mysql_error(con));
        logGW("dbInsertLGK_ACK> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbInsertLGK_ACK> Failed to insert ACK message.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbInsertLGK_ACK> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbInsertLGK_ACK> Failed to retrieve the result set.");
    }

    int result = 0;
    
    if ((row = mysql_fetch_row(res_set)) != NULL)
    {
        result = (row[0] == NULL) ? 0 : atoi(row[0]);
    }

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return result;
}

int dbCheckBlackList(MYSQL *con, int limit, char *store_procedure)
{
    char query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;

    sprintf(query_str, "CALL %s(0, 1, %d)", store_procedure, limit);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbCheckBlackList> Failed to check black list. Error: %s", mysql_error(con));
        logGW("dbCheckBlackList> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbCheckBlackList> Failed to check black list.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbCheckBlackList> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbCheckBlackList> Failed to retrieve the result set.");
    }

    int total   = 0;
    int black   = 0;
    int noblack = 0;
    
    if ((row = mysql_fetch_row(res_set)) != NULL)
    {
        total   = (row[0] == NULL) ? 0 : atoi(row[0]);
        black   = (row[1] == NULL) ? 0 : atoi(row[1]);
        noblack = (row[2] == NULL) ? 0 : atoi(row[2]);
        
        if (total > 0)
        {
			logGW("=> Check balck list - total: %d  blacklisted: %d  fordispatch: %d", total, black, noblack);
			syncLog();
		}
    }

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return total;
}

int dbStartCampaign(MYSQL *con, char *store_procedure)
{
    char query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;

    sprintf(query_str, "CALL %s()", store_procedure);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbStartCampaign> Failed to start campaign. Error: %s", mysql_error(con));
        logGW("dbStartCampaign> SQL: %s", query_str);
 		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbStartCampaign> Failed to start campaign.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbStartCampaign> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbStartCampaign> Failed to retrieve the result set.");
    }

    int rowCount = 0;
    
    int id;
    char description[50 + 1];
    int total;
    
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {
        id   = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(description, (row[1] == NULL) ? "" : row[1]);
        total = (row[2] == NULL) ? 0 : atoi(row[2]);
        
        logGW("=> Start campaign - id_campaign: %d description: ->%s<- total: %d", id, description, total);
        rowCount++;
    }
    syncLog();

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return rowCount;
}

int dbMoveCampaign(MYSQL *con, char *store_procedure)
{
    char query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;

    sprintf(query_str, "CALL %s()", store_procedure);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbMoveCampaign> Failed to move campaign. Error: %s", mysql_error(con));
        logGW("dbMoveCampaign> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbMoveCampaign> Failed to move campaign.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbMoveCampaign> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbMoveCampaign> Failed to retrieve the result set.");
    }

    int rowCount = 0;
    
    int id;
    char description[50 + 1];
    int total;
    
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {    syncLog();

        id   = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(description, (row[1] == NULL) ? "" : row[1]);
        total = (row[2] == NULL) ? 0 : atoi(row[2]);
        
        logGW("=> Move campaign - id_campaign: %d description: ->%s<- total: %d", id, description, total);
        rowCount++;
    }
    syncLog();

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return rowCount;
}

int dbEndCampaign(MYSQL *con, char *store_procedure)
{
    char query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;

    sprintf(query_str, "CALL %s()", store_procedure);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbEndCampaign> Failed to end campaign. Error: %s", mysql_error(con));
        logGW("dbEndCampaign> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbEndCampaign> Failed to end campaign.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbEndCampaign> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbEndCampaign> Failed to retrieve the result set.");
    }

    int rowCount = 0;
    
    int id;
    char description[50 + 1];
    int total;
    
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {
        id   = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(description, (row[1] == NULL) ? "" : row[1]);
        total = (row[2] == NULL) ? 0 : atoi(row[2]);
        
        logGW("=> End campaign - id_campaign: %d description: ->%s<- total: %d", id, description, total);
        rowCount++;
    }
    syncLog();

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return rowCount;
}

int dbPostCampaign(MYSQL *con, int delta, char *store_procedure)
{
    char query_str[2048];

    MYSQL_RES   *res_set;
    MYSQL_ROW   row;

    sprintf(query_str, "CALL %s(%d)", store_procedure, delta);

    if (mysql_query(con, query_str) != 0)
    {
        logGW("dbPostCampaign> Failed to exec post end campaign. Error: %s", mysql_error(con));
        logGW("dbPostCampaign> SQL: %s", query_str);
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbPostCampaign> Failed to exec post end campaign.");
    }

    res_set = mysql_store_result(con);
    if (res_set == NULL)
    {
        logGW("dbPostCampaign> Failed to retrieve the result set: %s", mysql_error(con));
		logGW("Force exit !");
        mysql_close(con);
        traceExit(-1, "dbPostCampaign> Failed to retrieve the result set.");
    }

    int rowCount = 0;
    
    int id;
    char description[50 + 1];
    int total;
    int affected;
    
    while ((row = mysql_fetch_row(res_set)) != NULL)
    {
        id   = (row[0] == NULL) ? 0 : atoi(row[0]);
        strcpy(description, (row[1] == NULL) ? "" : row[1]);
        total = (row[2] == NULL) ? 0 : atoi(row[2]);
        affected = (row[3] == NULL) ? 0 : atoi(row[3]);
        
        logGW("=> Post campaign - id_campaign: %d description: ->%s<- total: %d anulados: %d", id, description, total, affected);
        rowCount++;
    }
    syncLog();

    mysql_free_result(res_set);

    // Ignore status result set
    if (mysql_more_results(con))
    {
        mysql_next_result(con);
    }

    return rowCount;
}

/*************************************************/

void attachQDMT(transport_cfg *cfg)
{
    srand(getpid());
    openMySQL(&(QUEUEconn.conn), cfg->app_dbhost, cfg->app_dbname, cfg->app_dbuser, cfg->app_dbpass, cfg->app_dbport);
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_init(&QUEUEconn.dbaccess_mutex, NULL);
    return;
}

int getStatusDMT(statusQDMT *statusDMT, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbStatusDMT(&(QUEUEconn.conn), statusDMT, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int getRowsDMT(msgQDMT *queueDMT, int company, int queue, int queue_size, int batch_size, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbSelectDMT(&(QUEUEconn.conn), queueDMT, company, queue, queue_size, batch_size, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int modRowDMT(int id, char *status, char *operator, int errCode, char *errText, char *gkv_id, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbUpdateDMT(&(QUEUEconn.conn), id, status, operator, errCode, errText, gkv_id, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

void detachQDMT()
{
	closeMySQL(&(QUEUEconn.conn));	
}

/*************************************************/

void attachQDMO(transport_cfg *cfg)
{
    srand(getpid());
    openMySQL(&(QUEUEconn.conn), cfg->app_dbhost, cfg->app_dbname, cfg->app_dbuser, cfg->app_dbpass, cfg->app_dbport);
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_init(&QUEUEconn.dbaccess_mutex, NULL);
    return;
}

int getStatusDMO(statusQDMO *statusDMO, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbStatusDMO(&(QUEUEconn.conn), statusDMO, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int getRowsDMO(msgQDMO *queueSMS, int company, int queue, int queue_size, int batch_size, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbSelectDMO(&(QUEUEconn.conn), queueSMS, company, queue, queue_size, batch_size, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int modRowDMO(int id, char *status, int delta, int errCode, char *errText, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbUpdateDMO(&(QUEUEconn.conn), id, status, delta, errCode, errText, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

void detachQDMO()
{
	closeMySQL(&(QUEUEconn.conn));	
}

/*************************************************/

void attachQDACK(transport_cfg *cfg)
{
    srand(getpid());
    openMySQL(&(QUEUEconn.conn), cfg->app_dbhost, cfg->app_dbname, cfg->app_dbuser, cfg->app_dbpass, cfg->app_dbport);
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_init(&QUEUEconn.dbaccess_mutex, NULL);
    return;
}

int getStatusDACK(statusQDACK *statusDACK, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbStatusDACK(&(QUEUEconn.conn), statusDACK, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int getRowsDACK(msgQDACK *queueSMS, int company, int queue, int queue_size, int batch_size, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbSelectDACK(&(QUEUEconn.conn), queueSMS, company, queue, queue_size, batch_size, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int modRowDACK(int id, char *status, int delta, int errCode, char *errText, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbUpdateDACK(&(QUEUEconn.conn), id, status, delta, errCode, errText, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

void detachQDACK()
{
	closeMySQL(&(QUEUEconn.conn));	
}

/*************************************************/

void attachQLGK(transport_cfg *cfg)
{
    srand(getpid());
    openMySQL(&(QUEUEconn.conn), cfg->app_dbhost, cfg->app_dbname, cfg->app_dbuser, cfg->app_dbpass, cfg->app_dbport);
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_init(&QUEUEconn.dbaccess_mutex, NULL);
    return;
}

int modRowLGK(int id, char *status, char *operator, int errCode, char *errText, char *gkv_id, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbUpdateLGK(&(QUEUEconn.conn), id, status, operator, errCode, errText, gkv_id, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int insRowLGK_MO(char *gkv_token, char *operator, char *msisdn, char *message, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbInsertLGK_MO(&(QUEUEconn.conn), gkv_token, operator, msisdn, message, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int insRowLGK_ACK(char *app_id, char *msg_id, char *id_gkv, int ref_mt_id, char *phone, char *short_number, char *carrier, char *ack_status, char *ack_reason, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbInsertLGK_ACK(&(QUEUEconn.conn), app_id, msg_id, id_gkv, ref_mt_id, phone, short_number, carrier, ack_status, ack_reason, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

void detachQLGK()
{
	closeMySQL(&(QUEUEconn.conn));	
}

/*************************************************/

void attachQMSD(transport_cfg *cfg)
{
    srand(getpid());
    openMySQL(&(QUEUEconn.conn), cfg->app_dbhost, cfg->app_dbname, cfg->app_dbuser, cfg->app_dbpass, cfg->app_dbport);
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_init(&QUEUEconn.dbaccess_mutex, NULL);
    return;
}

int checkBlackList(int limit, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbCheckBlackList(&(QUEUEconn.conn), limit, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int startCampaign(char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbStartCampaign(&(QUEUEconn.conn), store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int moveCampaign(char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbMoveCampaign(&(QUEUEconn.conn), store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int endCampaign(char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbEndCampaign(&(QUEUEconn.conn), store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

int postCampaign(int delta, char *store_procedure)
{
    int ret = 0;
    QUEUEconn.lastAccess = time(NULL);
    pthread_mutex_lock(&QUEUEconn.dbaccess_mutex);
    ret = dbPostCampaign(&(QUEUEconn.conn), delta, store_procedure);
    pthread_mutex_unlock(&QUEUEconn.dbaccess_mutex);
    return ret;
}

void detachQMSD()
{
	closeMySQL(&(QUEUEconn.conn));	
}

/*************************************************/
