# Makefile for server dispatchACK

CC         = clang
CCFLAGS    = -ggdb -O -Wall -Wno-pointer-sign -ggdb -D_REENTRANT -D_FILE_OFFSET_BITS=64
CCINCLUDES = -I../Common
LD         = $(CC)
LDLIBS     = -lpthread -lcurl -lm -L/usr/lib64/mysql/ -lmysqlclient

BIN        = ./dispatchACK
OBJ        = main.o cJSON.o service.o

all:	dispatchACK

main.o: main.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c main.c

cJSON.o: cJSON.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c cJSON.c

service.o: ../Common/service.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c ../Common/service.c

dispatchACK: $(OBJ)
	$(LD) -o $@ main.o cJSON.o service.o $(LDFLAGS) $(LDLIBS)
	strip dispatchACK

clean:
	rm -f core *.o $(BIN)
