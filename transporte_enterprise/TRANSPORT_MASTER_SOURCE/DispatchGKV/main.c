#include <unistd.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <sys/wait.h>
#include <dirent.h>
#include <curl/curl.h>
#include "cJSON.h"
#include <service.h>
#include <pthread.h>

#include <sys/time.h>
#include <sys/resource.h>       

#define FORCE_EXIT_CODE    -999999

typedef struct
{
  char *memory;
  size_t size;
} MemoryStruct;

typedef struct
{  
    CURL *curl_handle;
    MemoryStruct chunk;
} WSHandle;

transport_cfg cfg;

char abnormalExit = 0;

char procTag[80 + 1];

char* LHOME = NULL;

controlDMT *ctlThreads = NULL;
pthread_mutex_t ctlThreads_mutex;

/**********************************************/
/**********************************************/

void getConfiguration()
{
    char cfgFile[100 + 1];

    FILE *fd = NULL;

    sprintf(cfgFile, "%s/conf/params.cfg", LHOME);

    if ((fd = fopen(cfgFile, "r")) != NULL)
    {
        readConfigFile(fd, &cfg);
        fclose(fd);
    }
    else
    {
        logGW("Can't open config file: %s", cfgFile);
        logGW("Force exit !");
        exit(-1);
    }
}

int checkProcStop()
{
    char ctlFile[100 + 1];

    FILE *fd = NULL;

    sprintf(ctlFile, "%s/run/%s.cmd", LHOME, procTag);

    if ((fd = fopen(ctlFile, "r")) != NULL)
    {
        fclose(fd);
        return 1;
    }
    else
    {
        return 0;
    }
}

void forceProcStop()
{
    char ctlFile[100 + 1];

    FILE *fd = NULL;

    sprintf(ctlFile, "%s/run/%s.cmd", LHOME, procTag);

    if ((fd = fopen(ctlFile, "w")) != NULL)
    {
        fclose(fd);
		logGW("Stop control file created.");
		syncLog();
    }
    else
    {
		logGW("Can't create stop control file. Force exit !");
		syncLog();
		exit(-1);
    }
}

/*****************************************************************/

static size_t WriteMemoryCallback(void *contents, size_t size, size_t nmemb, void *userp)
{
    size_t realsize = size * nmemb;
    MemoryStruct *mem = (MemoryStruct *)userp;
   
    mem->memory = realloc(mem->memory, mem->size + realsize + 1);
    if(mem->memory == NULL)
    {
        /* out of memory! */
        logGW("ABORT: not enough memory (realloc returned NULL)."); syncLog();
        exit(-1);
    }  
   
    memcpy(&(mem->memory[mem->size]), contents, realsize);
    mem->size += realsize;
    mem->memory[mem->size] = 0;
   
    return realsize;
}  

void httpInit(WSHandle *ws)  
{
       
    /* init the curl session */
    ws->curl_handle = curl_easy_init();
    if (ws->curl_handle == NULL)
    {
        logGW("ABORT: curl_easy_init returned NULL."); syncLog();
        exit(-1);
    }  
       
    ws->chunk.memory = malloc(1);  /* will be grown as needed by the realloc above */
    ws->chunk.size = 0;            /* no data at this point */
       
    /* some servers don't like requests that are made without a user-agent field, so we provide one */
    curl_easy_setopt(ws->curl_handle, CURLOPT_USERAGENT, "libcurl-agent/1.0");
       
    /* configure headers for json request */
    struct curl_slist* headers = NULL;
    headers = curl_slist_append(headers, "Accept: application/json");
    headers = curl_slist_append(headers, "Content-Type: application/json");
    headers = curl_slist_append(headers, "charsets: utf-8");
    headers = curl_slist_append(headers, "Expect:");
    curl_easy_setopt(ws->curl_handle, CURLOPT_HTTPHEADER, headers);
       
    /* send all data to this function  */
    curl_easy_setopt(ws->curl_handle, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
       
    /* we pass our 'chunk' struct to the callback function */
    curl_easy_setopt(ws->curl_handle, CURLOPT_WRITEDATA, (void *)&(ws->chunk));
       
    /* ask libcurl to show us the verbose output */
    curl_easy_setopt(ws->curl_handle, CURLOPT_VERBOSE, 0L);

    return;
}

int sendHTTP(WSHandle *ws, char *request, char **response, char *reason)  
{ 
    CURLcode res;
   
    CURL *curl_handle = ws->curl_handle;
 
    ws->chunk.size = 0;    /* no data at this point */
   
    /* specify URL to post */
    curl_easy_setopt(curl_handle, CURLOPT_URL, cfg.dgk_laws_url);
    curl_easy_setopt(curl_handle, CURLOPT_FOLLOWLOCATION, 1L);

    /* specify data to post */
    curl_easy_setopt(curl_handle, CURLOPT_POSTFIELDS, request);
 
    /* get it! */
    res = curl_easy_perform(curl_handle);
 
    /* check for errors */
    if (res != CURLE_OK)
    {
        strncpy(reason, curl_easy_strerror(res), 255);
        reason[255] = '\0';
        logGW("Failed to dispatch to GKV: %s", curl_easy_strerror(res)); syncLog();
        return -1;
    }  
    else
    {
        // success
       
        (ws->chunk).memory[(ws->chunk).size] = '\0';
        *response = malloc((ws->chunk).size + 1);
        memcpy(*response, (ws->chunk).memory, (ws->chunk).size + 1);
       
        long http_code = 0;
        curl_easy_getinfo (curl_handle, CURLINFO_RESPONSE_CODE, &http_code);
       
        if (http_code >= 200 && http_code <= 299)
        {
            //Succeeded
            reason[0] = '\0';
            logGW("Dispatch OK to GKV. Response http code (%ld).", http_code); syncLog();
            return 0;
        }  
        else
        {
            //Failed
            sprintf(reason, "Response http code (%ld)", http_code);
            logGW("Failed to dispatch to GKV: %s", reason); syncLog();
            return -1;
        }  
    }  
}

void httpFinish(WSHandle *ws)
{       
    /* cleanup curl stuff */
    curl_easy_cleanup(ws->curl_handle);
       
    if (ws->chunk.memory)
    {
        free(ws->chunk.memory);
    }  

    return;
}

int processBatch(WSHandle *ws, int company, int queue, char *gkv_token)
{
    int arrId[cfg.dgk_batch_size];
    msgQDMT queueDMT[cfg.dgk_batch_size];
    
    int rows = getRowsDMT(queueDMT, company, queue, cfg.dgk_threads_count, cfg.dgk_batch_size, cfg.dgk_sp_select_mt);

    if (rows > 0)
    {
        int i, msgs_to_send = 0;

        cJSON *app, *message, *destination, *item;
       
        cJSON *root = cJSON_CreateObject();
        cJSON_AddItemToObject(root, "app", app = cJSON_CreateObject());
        cJSON_AddStringToObject(app, "token", gkv_token);
        cJSON_AddItemToObject(root, "message", message = cJSON_CreateArray());

        logGW("-------------------------");
        logGW("rows fetched for company %d and queue %d: %d", company, queue, rows);
        logGW("-------------------------");
        for (i = 0; i < rows; i++)
        {
            char msisdn[30 + 1];
            char msg_id_app[45 + 1];
            unsigned int id = queueDMT[i].id;
            sprintf(msg_id_app, "%s%010d", cfg.app_prefix, id);

            format_msisdn(msisdn, queueDMT[i].recipientId);

            item = cJSON_CreateObject();
            cJSON_AddStringToObject(item, "type", "smspush");
            cJSON_AddBoolToObject(item, "billing", cJSON_False);
            cJSON_AddBoolToObject(item, "validity", cJSON_True);
            cJSON_AddStringToObject(item, "mode", "text");
            cJSON_AddStringToObject(item, "content", queueDMT[i].msgText);
            cJSON_AddStringToObject(item, "area", queueDMT[i].cc);
            cJSON_AddItemToObject(item, "destination", destination = cJSON_CreateObject());
            cJSON_AddStringToObject(destination, "msgIdApp", msg_id_app);
            cJSON_AddStringToObject(destination, "msisdn", msisdn);

            cJSON_AddItemToArray(message, item);
            arrId[msgs_to_send] = id;
            msgs_to_send++;
            
            logGW("id: %u company: %d msisdn: %s msgIdApp: %s pos: %d msg: ->%s<-", id, company, msisdn, msg_id_app, i, queueDMT[i].msgText);

            //XXX
            //modRowDMT(id, "SENDING", NULL, 0, "Sending message to GKV ...", NULL);
            //Deshacer el sending si hay error de comunicacion
            //modRowDMT(arrId[idx], "PUSHED", NULL, 0, "", element_msgIdGK->valuestring, cfg.dgk_sp_update_mt);
        }
        syncLog();

        if (msgs_to_send > 0)
        {
            char *request = cJSON_PrintUnformatted(root);
            char *response = NULL;
            char reason[255 + 1] = "";

            logGW("=====================================");
            logGW("request for company %d and queue %d: %s", company, queue, request ? request : "");
            logGW("=====================================");
            syncLog();

            int ret = sendHTTP(ws, request, &response, reason);
            
            if (request != NULL)
            {
				free(request);
			}

            if (ret == 0)
            {
                // revisar respuesta
                logGW("=====================================");
                logGW("response for company %d and queue %d: %s", company, queue, response ? response : "");
                logGW("=====================================");
                syncLog();

                cJSON *json = cJSON_Parse(response);

                if (json)
                {
                    if (json->type == cJSON_Object)
                    {
                        cJSON *errCode = cJSON_GetObjectItem(json, "errCode");
                        if (errCode->valueint == 0)
                        {
                            cJSON *result = cJSON_GetObjectItem(json, "result");
                            if (result->type == cJSON_Array)
                            {
								if (cJSON_GetArraySize(result) != msgs_to_send)
								{
									logGW("ERROR: Invalid result array size for company %d and queue %d.", company, queue);
									forceProcStop();
									rows = FORCE_EXIT_CODE;
								}
								else
								{
									int idx;
									for (idx = 0; idx < cJSON_GetArraySize(result); idx++)
									{
										cJSON *element = cJSON_GetArrayItem(result, idx);										
										if (element != NULL && element->type == cJSON_Object)
										{
											cJSON *element_msgIdGK  = cJSON_GetObjectItem(element, "msgIdGK");
											cJSON *element_msgIdApp = cJSON_GetObjectItem(element, "msgIdApp");
											
											if (element_msgIdGK == NULL || element_msgIdGK->type != cJSON_String ||
											    element_msgIdApp == NULL || element_msgIdApp->type != cJSON_String)
											{
												logGW("ERROR: Invalid JSON item at position %d for company %d and queue %d.", idx, company, queue);
												forceProcStop();
												rows = FORCE_EXIT_CODE;
												break;
											}
											else
											{
												char msg_id_app[45 + 1];
												sprintf(msg_id_app, "%s%010d", cfg.app_prefix, arrId[idx]);

												if (strcmp(msg_id_app, element_msgIdApp->valuestring) != 0)
												{
													logGW("WARNING: Invalid msgIdApp at position %d for company %d and queue %d", idx, company, queue);
												}

												modRowDMT(arrId[idx], "DISPATCHED", NULL, 0, "", element_msgIdGK->valuestring, cfg.dgk_sp_update_mt);
											}
										}
										else
										{
											logGW("ERROR: Invalid JSON item (null or not an object) at position %d for company %d and queue %d.", idx, company, queue);
											forceProcStop();
											rows = FORCE_EXIT_CODE;
											break;
										}										
									}
								}								
                            }
                            else
                            {
                                logGW("ERROR: Invalid 'result' type for company %d and queue %d.", company, queue);
                                forceProcStop();
                                rows = FORCE_EXIT_CODE;
                            }
                        }
                        else
                        {
							cJSON *errText = cJSON_GetObjectItem(json, "errText");
							if (errText != NULL && errText->type == cJSON_String && errText->valuestring != NULL)
							{
								logGW("== errCode: %d '%s' for company %d and queue %d ===", errCode->valueint, errText->valuestring, company, queue);
							}
							else
							{
								logGW("== errCode: %d 'No info' for company %d and queue %d ===", errCode->valueint, company, queue);
							}
                            rows = -1;
                        }  
                    }  
                    else
                    {
                        logGW("ERROR: Invalid response JSON type for company %d and queue %d.", company, queue);
                        forceProcStop();
						rows = FORCE_EXIT_CODE;
                    } 
                }
                else
                {  
                    logGW("ERROR: Invalid response JSON (failed to parse) for company %d and queue %d.", company, queue);
					forceProcStop();
                    rows = FORCE_EXIT_CODE;
                }

                cJSON_Delete(json);
                
                if (response != NULL)
                {
					free(response);
				}
            }
            else
            {
                logGW("Failed to send HTTP request for company %d and queue %d. Reason: %d -> %s", company, queue, ret, reason);
                rows = -1;
            }
        }
        else
        {
            logGW("No rows to send for company %d and queue %d !", company, queue);
        }

        /* clean data */
        cJSON_Delete(root);

        controlThrot(cfg.dgk_batch_rps);
    }
    
    return rows;
}

int isThreadRunning(int company, int queue)
{
	int result = 0;
	
	pthread_mutex_lock(&ctlThreads_mutex);

    controlDMT *ctl = ctlThreads;
	while (ctl != NULL)
	{
		if (ctl->company == company && ctl->queue == queue)
		{
			result = 1;
			break;
		}
		ctl = ctl->next;
	}
	
	pthread_mutex_unlock(&ctlThreads_mutex);
	
	return result;
}

void checkInactiveThreads()
{
	pthread_mutex_lock(&ctlThreads_mutex);

    if (ctlThreads != NULL)
    {
		controlDMT *prv, *ctl, *del;

        /* check from second node if exists to final */	
        prv = ctlThreads;	
		ctl = ctlThreads->next;	
		while (ctl != NULL)
		{
			if (ctl->threadStatus == 'I')
			{
				if (pthread_join(ctl->thread, NULL) != 0)
				{
					logGW("ERROR on pthread_join for company %d and queue %d. Force exit !", ctl->company, ctl->queue);
					exit(-1);
				}
				prv->next = ctl->next;
				del = ctl;
				ctl = ctl->next;
				free(del);
			}
			else
			{
				prv = ctl;
				ctl = ctl->next;
			}
		}
		
		/* check firt node */
		ctl = ctlThreads;
		if (ctl->threadStatus == 'I')
		{
			if (pthread_join(ctl->thread, NULL) != 0)
			{
				logGW("ERROR on pthread_join for company %d and queue %d. Force exit !", ctl->company, ctl->queue);
				exit(-1);
			}
			ctlThreads = ctl->next;
			free(ctl);
		}
	}

	pthread_mutex_unlock(&ctlThreads_mutex);	
}

void stopAllThreads()
{
	/* Signal all active threads to stop */
	pthread_mutex_lock(&ctlThreads_mutex);
	{
		controlDMT *ctl = ctlThreads;
		while (ctl != NULL)
		{
			if (ctl->threadStatus == 'A')
			{
				ctl->threadStatus = 'S';
			}
			ctl = ctl->next;
		}
	}
	pthread_mutex_unlock(&ctlThreads_mutex);
	
	/* Wait for thread exit */
	{
		controlDMT *ctl = ctlThreads;
		if (ctl == NULL)
		{
			logGW("No active dispatch threads to stop.");
		}
		
		while (ctl != NULL)
		{
			if (pthread_join(ctl->thread, NULL) != 0)
			{
				logGW("WARNING: Ignore error on pthread_join for company %d and queue %d.", ctl->company, ctl->queue); syncLog();
			}
			ctl = ctl->next;
		}
	}	
}

void *serviceDispatch(void *ptr)
{
	controlDMT *ctl = (controlDMT*)ptr;
	
	int company = ctl->company;
	int queue = ctl->queue;
	
	char gkv_token[45 + 1];
	strcpy(gkv_token, ctl->gkv_token);

    WSHandle wsHndl;
    
    /* Init curl session for this service dispatcher */
    httpInit(&wsHndl);
	
	logGW("Dispatch thread for company %d and queue %d started.", company, queue); syncLog();

    time_t lastShown = 0;
    time_t lastActivity = time(NULL);

    char DONE = 0;
    
	while (! DONE)
	{
        int ret = processBatch(&wsHndl, company, queue, gkv_token);
        if (ret < 0)
        {
			if (ret == FORCE_EXIT_CODE)
			{
				logGW("Failed to process task for company %d and queuue %d.", company, queue); syncLog();
				logGW("FORCE_EXIT_CODE has been generated.");
				abnormalExit++;
				break;
			}
			else
			{
				logGW("Failed to process task for company %d and queuue %d.", company, queue); syncLog();
				sleep(cfg.dgk_read_wait);
			}
        }
        else if (ret == 0)
        {
            if (time(NULL) - lastShown >= cfg.dgk_show_wait)
            {
                logGW("No rows processed for company %d and queue %d.", company, queue); syncLog();
                lastShown = time(NULL);
            }
            sleep(cfg.dgk_read_wait);
        }
        else
        {
            lastShown = lastActivity = time(NULL);
            usleep(1);  /* release cpu to help threads schedule */
        }

        /* Check admin status for this dispatch thread */
        		
		pthread_mutex_lock(&ctlThreads_mutex);
		{
			if (ctl->threadStatus == 'A')
			{
				if (time(NULL) - lastActivity > cfg.dgk_inactive_time)
				{
					ctl->threadStatus = 'I';
					logGW("Inactivity time reached (%ds) for company %d and queue %d.", cfg.dgk_inactive_time, company, queue);
					DONE++;
				}
			}
			else
			{
				DONE++;
			}
		}
		pthread_mutex_unlock(&ctlThreads_mutex);
	}
	
    /* Finish curl session for this service dispatcher */
    httpFinish(&wsHndl);

	logGW("Dispatch thread for company %d and queue %d stopped.", company, queue); syncLog();
	pthread_exit(NULL);
}

void createDispatchThread(int company, int queue, char *token)
{
	logGW("--- CREATE DISPATCH THREAD for company: %d - queue: %d  =>  gkv_token: %s ---", company, queue, token);
	
	controlDMT *ctl = malloc(sizeof(controlDMT));
	if (ctl == NULL)
	{
		logGW("ERROR in createDispatchThread: No memory available for create data control. Force exit !");
		exit(-1);
	}
		
	pthread_mutex_lock(&ctlThreads_mutex);
	{
		ctl->company = company;
		ctl->queue = queue;
		strcpy(ctl->gkv_token, token);
		ctl->threadStatus = 'A';

		ctl->next = ctlThreads;
		ctlThreads = ctl;

		/* initialized with default attributes */
		int ret;
		pthread_attr_t tattr;    

		ret = pthread_attr_init(&tattr);
		if (ret != 0)
		{
			logGW("ERROR in pthread_attr_init - return code: %d. Force exit !", ret);
			exit(-1);
		}

		ret = pthread_attr_setstacksize(&tattr, DISPATCH_STACK_SIZE);
		if (ret != 0)
		{
			logGW("ERROR in pthread_attr_setstacksize - return code: %d. Force exit !", ret);
			exit(-1);
		}

		ret = pthread_attr_setdetachstate(&tattr, PTHREAD_CREATE_JOINABLE);  /* default */
		if (ret != 0)
		{
			logGW("ERROR in pthread_attr_setdetachstate - return code: %d. Force exit !", ret);
			exit(-1);
		}

		ret = pthread_attr_setschedpolicy(&tattr, SCHED_RR);
		if (ret != 0)
		{
			logGW("ERROR in pthread_attr_setschedpolicy - return code: %d. Force exit !", ret);
			exit(-1);
		}

		/* create thread for dispatch company and queue */
		ret = pthread_create(&ctl->thread, &tattr, serviceDispatch, ctl);
		if (ret != 0)
		{
			logGW("ERROR in pthread_create - return code: %d.", ret);
			logGW("Check 'ulimit -u' for max user processes. Force exit !");
			exit(-1);
		}
	}
	pthread_mutex_unlock(&ctlThreads_mutex);
}

int controlTask()
{
	statusQDMT statusDMT[MAX_COMPANIES];

	int rows = getStatusDMT(statusDMT, cfg.dgk_sp_status_mt);
	if (rows > 0)
	{
		int i, j;
		
		for (i = 0; i < rows; i++)
		{
			for (j = 0; j < cfg.dgk_threads_count; j++)
			{
				if ( ! isThreadRunning(statusDMT[i].company, j))
				{
					createDispatchThread(statusDMT[i].company, j, statusDMT[i].gkv_token);
				}
			}
		}
	}

	checkInactiveThreads();

	return rows;
}

/**********************************************/

static void sigterm(int sig)
{
    logGW("========> Signal %d received. Ignored. <========", sig); syncLog();
}

int main(int argc, char **argv)
{
    LHOME = getenv("LHOME");

    if (LHOME == NULL)
    {
	#if 0 
        printf("Undefined LHOME environment variable.\n");
        exit(-1);
	#endif 
	LHOME = "/home/<USER>/GKV/";
    }

    DIR* dir = opendir(LHOME);
    if (dir)
    {  
         closedir(dir);
    }
    else   
    {
        printf("Invalid LHOME environment variable. Directory does not exists: %s\n", LHOME);
        exit(-1);
    }  
       
    if (argc != 2)
    {
        printf("Uso: %s <tag>\n", argv[0]);
        printf("Invalid argument.\n");
        exit(-1);
    }

    strcpy(procTag, argv[1]);

    initLog(LHOME, procTag);

    /***********************************/
    /* Genera un core si se requiere */
    struct rlimit r;

    r.rlim_cur = RLIM_INFINITY;
    r.rlim_max = RLIM_INFINITY;
    if (setrlimit(RLIMIT_CORE, &r) == -1)
    {
        logGW("Error in setrlimit()\n"); syncLog();
        return -1;
    }
    /***********************************/

    logGW("Starting process %s.", procTag);
    logGW("LHOME: '%s'", LHOME);
    syncLog();

    /* read configuration file */
    getConfiguration();
   
    /*************/

    /* Verificamos que no se este ejecutando otra instancia en el proceso padre */ 
    if (lockPID(LHOME, procTag) != 0)
    {
        logGW("Couldn't lock pid file. Maybe another instance is running. Process [%d] aborted.", getpid());
        exit(-1);
    }  
#ifdef __STAND_ALONE__
    /* Become a daemon */
    switch (fork())
    {
        case 0:
            break;
        case -1:
            logGW("Unable to become a daemon");
        default:
            exit(0);
    }

    if (setsid () == -1)
    {
        perror("setsid failed.");
        exit(-1);
    }  
 
    /* Bloqueamos el archivo pid de esta instancia en el proceso hijo */
    sleep(1); 
    if (lockPID(LHOME, procTag) != 0)
    {
        logGW("Couldn't lock pid file. Maybe another instance is running. Process [%d] aborted.", getpid());
        exit(-1);
    }  
#endif 
    /* Catch various termination signals. */
    signal(SIGTERM , sigterm);
    signal(SIGINT  , sigterm);
    signal(SIGHUP  , sigterm);

    /* Modificamos a _IOLBF (line buffered) los buffers STDOUT y STDERR */
    setlinebuf(stdout);
    setlinebuf(stderr);

    /* show configuration file */
    showConfig(&cfg);

    /* Start the dispatch GKV daemon */
    logGW("Daemon dispatchGKV started."); syncLog();

    /* Initialization */
    attachQDMT(&cfg);
    pthread_mutex_init(&ctlThreads_mutex, NULL);
    
    /* Init curl */    
    curl_global_init(CURL_GLOBAL_ALL);

    time_t lastShown = 0;

    while (! checkProcStop())
    {
        int ret = controlTask();
        if (ret < 0)
        {
            logGW("Failure on controlTask. Return code: %d. Force exit !", ret); syncLog();
            exit(-1);
        }
        else if (ret == 0)
        {
            if (time(NULL) - lastShown >= cfg.dgk_show_wait)
            {
                logGW("No message to dispatch."); syncLog();
                lastShown = time(NULL);
            }
        }
        else
        {
            lastShown = time(NULL);
        }

		sleep(cfg.dgk_read_wait);
    }

    /* Stop all active threads */
	logGW("Stopping all active dispatch threads ..."); syncLog();
    stopAllThreads();

    if (abnormalExit)
    {
		logGW("#########################################################");
		logGW("#########################################################");
		logGW("");
		logGW("Se fuerza la detención anormal del proceso ya que se ha encontrado");
		logGW("una respuesta desde GKV fuera de protocolo que debe ser analizada.");
		logGW("Para más información busque el string FORCE_EXIT_CODE más arriba en este log.");
		logGW("");
		logGW("NO REINICIE ESTE PROCESO HASTA RESOLVER EL PROBLEMA DE PROTOCOLO ASOCIADO A GKV");
		logGW("");
		logGW("#########################################################");
		logGW("#########################################################");
	}
	
    logGW("Process stopped.");
    syncLog();

    /* Cleanup curl */
    curl_global_cleanup();

	/* Free resources */
	detachQDMT();
	
    return 0;
}

/* ######################## EOF ############################ */
