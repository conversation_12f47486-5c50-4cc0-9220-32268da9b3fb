# Makefile for server dispatchGKV

CC         = clang
CCFLAGS    = -ggdb -O -Wall -Wno-pointer-sign -ggdb -D_REENTRANT -D_FILE_OFFSET_BITS=64
CCINCLUDES = -I../Common `/opt/homebrew/bin/mysql_config --include`
LD         = $(CC)
#LDLIBS     = -lpthread -lcurl -lm  `/opt/homebrew/bin/mysql_config --libs` -L/opt/homebrew/lib
LDLIBS     = -lpthread -lcurl -lm -L/usr/lib64/mysql/ -lmysqlclient

BIN        = ./dispatchGKV
OBJ        = main.o cJSON.o service.o

all:	dispatchGKV

main.o: main.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c main.c

cJSON.o: cJSON.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c cJSON.c

service.o: ../Common/service.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c ../Common/service.c

dispatchGKV: $(OBJ)
	$(LD) -o $@ main.o cJSON.o service.o $(LDFLAGS) $(LDLIBS)
#	strip dispatchGKV

clean:
	rm -f core *.o $(BIN)
