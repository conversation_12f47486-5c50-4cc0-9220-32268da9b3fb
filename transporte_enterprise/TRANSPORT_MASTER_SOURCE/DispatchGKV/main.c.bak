#include <unistd.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <sys/wait.h>
#include <dirent.h>
#include <curl/curl.h>
#include "cJSON.h"
#include <service.h>

typedef struct
{
  char *memory;
  size_t size;
} MemoryStruct;

typedef struct
{  
    CURL *curl_handle;
    MemoryStruct chunk;
} WSHandle;

transport_cfg cfg;

msgQDMT  *queueDMT;
WSHandle wsPool;

char procTag[80 + 1];
time_t lastShown = 0;

char* LHOME = NULL;

/**********************************************/

// TODO
// Trap signals

/**********************************************/

void getConfiguration()
{
    char cfgFile[100 + 1];

    FILE *fd = NULL;

    sprintf(cfgFile, "%s/conf/params.cfg", LHOME);

    if ((fd = fopen(cfgFile, "r")) != NULL)
    {
        readConfigFile(fd, &cfg);
        fclose(fd);
    }
    else
    {
        logGW("Can't open config file: %s", cfgFile);
        logGW("Force exit!");
        exit(-1);
    }
}

int checkProcStop()
{
    char ctlFile[100 + 1];

    FILE *fd = NULL;

    sprintf(ctlFile, "%s/run/%s.cmd", LHOME, procTag);

    if ((fd = fopen(ctlFile, "r")) != NULL)
    {
        fclose(fd);
        return 1;
    }
    else
    {
        return 0;
    }
}

/*****************************************************************/

static size_t WriteMemoryCallback(void *contents, size_t size, size_t nmemb, void *userp)
{
    size_t realsize = size * nmemb;
    MemoryStruct *mem = (MemoryStruct *)userp;
   
    mem->memory = realloc(mem->memory, mem->size + realsize + 1);
    if(mem->memory == NULL)
    {
        /* out of memory! */
        logGW("ABORT: not enough memory (realloc returned NULL)."); syncLog();
        exit(-1);
    }  
   
    memcpy(&(mem->memory[mem->size]), contents, realsize);
    mem->size += realsize;
    mem->memory[mem->size] = 0;
   
    return realsize;
}  

void httpInit()  
{
    WSHandle *ws;

    curl_global_init(CURL_GLOBAL_ALL);
   
    ws = &wsPool;
       
    /* init the curl session */
    ws->curl_handle = curl_easy_init();
    if (ws->curl_handle == NULL)
    {
        logGW("ABORT: curl_easy_init returned NULL."); syncLog();
        exit(-1);
    }  
       
    ws->chunk.memory = malloc(1);  /* will be grown as needed by the realloc above */
    ws->chunk.size = 0;            /* no data at this point */
       
    /* some servers don't like requests that are made without a user-agent field, so we provide one */
    curl_easy_setopt(ws->curl_handle, CURLOPT_USERAGENT, "libcurl-agent/1.0");
       
    /* configure headers for json request */
    struct curl_slist* headers = NULL;
    headers = curl_slist_append(headers, "Accept: application/json");
    headers = curl_slist_append(headers, "Content-Type: application/json");
    headers = curl_slist_append(headers, "charsets: utf-8");
    headers = curl_slist_append(headers, "Expect:");
    curl_easy_setopt(ws->curl_handle, CURLOPT_HTTPHEADER, headers);
       
    /* send all data to this function  */
    curl_easy_setopt(ws->curl_handle, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
       
    /* we pass our 'chunk' struct to the callback function */
    curl_easy_setopt(ws->curl_handle, CURLOPT_WRITEDATA, (void *)&(ws->chunk));
       
    /* ask libcurl to show us the verbose output */
    curl_easy_setopt(ws->curl_handle, CURLOPT_VERBOSE, 0L);

    return;
}

int sendHTTP(char *request, char **response, char *reason)  
{ 
    CURLcode res; 
   
    WSHandle *ws = &wsPool;
    CURL *curl_handle = ws->curl_handle;
 
    ws->chunk.size = 0;    /* no data at this point */
   
    /* specify URL to post */
    curl_easy_setopt(curl_handle, CURLOPT_URL, cfg.dgk_laws_url);
    curl_easy_setopt(curl_handle, CURLOPT_FOLLOWLOCATION, 1L);

    /* specify data to post */
    curl_easy_setopt(curl_handle, CURLOPT_POSTFIELDS, request);
 
    /* get it! */
    res = curl_easy_perform(curl_handle);
 
    /* check for errors */
    if (res != CURLE_OK)
    {
        strncpy(reason, curl_easy_strerror(res), 255);
        reason[255] = '\0';
        logGW("Failed to dispatch to GKV: %s", curl_easy_strerror(res)); syncLog();
        return -1;
    }  
    else
    {
        // success
       
        (ws->chunk).memory[(ws->chunk).size] = '\0';
        *response = malloc((ws->chunk).size + 1);
        memcpy(*response, (ws->chunk).memory, (ws->chunk).size + 1);
       
        long http_code = 0;
        curl_easy_getinfo (curl_handle, CURLINFO_RESPONSE_CODE, &http_code);
       
        if (http_code >= 200 && http_code <= 299)
        {
            //Succeeded
            reason[0] = '\0';
            logGW("Dispatch OK to GKV. Response http code (%ld).", http_code); syncLog();
            return 0;
        }  
        else
        {
            //Failed
            sprintf(reason, "Response http code (%ld)", http_code);
            logGW("Failed to dispatch to GKV: %s", reason); syncLog();
            return -1;
        }  
    }  
}

void httpFinish()
{
    WSHandle ws = wsPool;
       
    /* cleanup curl stuff */
    curl_easy_cleanup(ws.curl_handle);
       
    if (ws.chunk.memory)
    {
        free(ws.chunk.memory);
    }  

    /* we're done with libcurl, so clean it up */
    curl_global_cleanup();

    return;
}

int processBatch()
{
    int arrId[cfg.dgk_batch_size];
    int rows = getRowsDMT(queueDMT, cfg.dgk_app_token, 0, 1, cfg.dgk_batch_size, cfg.dgk_sp_select_mt);

    if (rows > 0)
    {
        int i, msgs_to_send = 0;

        cJSON *app, *message, *destination, *item;
       
        cJSON *root = cJSON_CreateObject();
        cJSON_AddItemToObject(root, "app", app = cJSON_CreateObject());
        cJSON_AddStringToObject(app, "token", cfg.dgk_app_token);
        cJSON_AddItemToObject(root, "message", message = cJSON_CreateArray());

        logGW("-------------------------");
        logGW("rows fetched: %d", rows);
        logGW("-------------------------");
        for (i = 0; i < rows; i++)
        {
            char msisdn[30 + 1];
            char msg_id_app[45 + 1];
            unsigned int id = queueDMT[i].id;
            sprintf(msg_id_app, "%s%010d", cfg.app_prefix, id);

            format_msisdn(msisdn, queueDMT[i].recipientId);

            item = cJSON_CreateObject();
            cJSON_AddStringToObject(item, "type", "smspush");
            cJSON_AddBoolToObject(item, "billing", cJSON_False);
            cJSON_AddBoolToObject(item, "validity", cJSON_True);
            cJSON_AddStringToObject(item, "mode", "text");
            cJSON_AddStringToObject(item, "content", queueDMT[i].msgText);
            cJSON_AddStringToObject(item, "area", queueDMT[i].cc);
            cJSON_AddItemToObject(item, "destination", destination = cJSON_CreateObject());
            cJSON_AddStringToObject(destination, "msgIdApp", msg_id_app);
            cJSON_AddStringToObject(destination, "msisdn", msisdn);

            cJSON_AddItemToArray(message, item);
            arrId[msgs_to_send] = id;
            msgs_to_send++;

            //XXX
            //modRowDMT(id, "SENDING", NULL, 0, "Sending message to GKV ...", NULL);
            //Deshacer el sending si hay error de comunicacion
            //modRowDMT(arrId[idx], "PUSHED", NULL, 0, "", element_msgIdGK->valuestring, cfg.dgk_sp_update_mt);
        }
        syncLog();

        if (msgs_to_send > 0)
        {
            char *request = cJSON_PrintUnformatted(root);
            char *response = NULL;
            char reason[255 + 1];

            logGW("=====================================");
            logGW("request: %s", request ? request : "");
            logGW("=====================================");
            syncLog();

            int ret = sendHTTP(request, &response, reason);
            logGW("ret: %d", ret); syncLog();

            if (ret == 0)
            {
                // revisar respuesta
                logGW("=====================================");
                logGW("response: %s", response ? response : "");
                logGW("=====================================");
                syncLog();

                cJSON *json = cJSON_Parse(response);

                if (json)
                {
                    if (json->type == cJSON_Object)
                    {
                        cJSON *errCode = cJSON_GetObjectItem(json, "errCode");
                        if (errCode->valueint == 0)
                        {
                            cJSON *result = cJSON_GetObjectItem(json, "result");
                            if (result->type == cJSON_Array)
                            {
                                int idx;
                                for (idx = 0; idx < cJSON_GetArraySize(result); idx++)
                                {
                                    char msg_id_app[45 + 1];
/*         
 * XXX Agregar validaciones de estructura
 */            
                                    cJSON *element = cJSON_GetArrayItem(result, idx);
                                    cJSON *element_msgIdGK  = cJSON_GetObjectItem(element, "msgIdGK");
                                    cJSON *element_msgIdApp = cJSON_GetObjectItem(element, "msgIdApp");
                                    sprintf(msg_id_app, "%s%010d", cfg.app_prefix, arrId[idx]);

                                    if (strcmp(msg_id_app, element_msgIdApp->valuestring) == 0)
                                    {
                                        modRowDMT(arrId[idx], "DISPATCHED", NULL, 0, "", element_msgIdGK->valuestring, cfg.dgk_sp_update_mt);
                                    }
                                    else
                                    {
                                        logGW("== Invalid msgIdApp ===");
                                        logGW("Force exit !");
                                        syncLog();
                                        exit(-1);
                                    }
                                }
                            }
                            else
                            {
                                logGW("== Invalid result type ===");
                                logGW("Force exit !");
                                syncLog();
                                exit(-1);
                            }
                        }
                        else
                        {
                            logGW("== errCode: %d ===", errCode->valueint);
                            logGW("Force exit !");
                            syncLog();
                            exit(-1);
                        }  
                    }  
                    else
                    {
                        logGW("== Invalid JSON type ===");
                        logGW("Force exit !");
                        syncLog();
                        exit(-1);
                    } 
                }
                else
                {  
                    logGW("== Invalid JSON ===");
                    logGW("Force exit !");
                    syncLog();
                    exit(-1);
                }          
                       
                cJSON_Delete(json);
            }
            else
            {
                logGW("Failed to send HTTP request.");
                rows = -1;
            }
        }
        else
        {
            logGW("No rows to send !");
        }

        /* clean data */
        cJSON_Delete(root);

        controlThrot(cfg.dgk_batch_rps);
    }

    return rows;
}

/**********************************************/

int main(int argc, char **argv)
{
    LHOME = getenv("LHOME");

    if (LHOME == NULL)
    {
        printf("Undefined LHOME environment variable.\n");
        exit(-1);
    }

    DIR* dir = opendir(LHOME);
    if (dir)
    {  
         closedir(dir);
    }
    else   
    {
        printf("Invalid LHOME environment variable. Directory does not exists: %s\n", LHOME);
        exit(-1);
    }  
       
    if (argc != 2)
    {
        printf("Uso: %s <tag>\n", argv[0]);
        printf("Invalid argument.\n");
        exit(-1);
    }

    strcpy(procTag, argv[1]);

    initLog(LHOME, procTag);

    /***********************************/
    /* Genera un core si se requiere */
    struct rlimit r;

    r.rlim_cur = RLIM_INFINITY;
    r.rlim_max = RLIM_INFINITY;
    if (setrlimit(RLIMIT_CORE, &r) == -1)
    {
        logGW("Error in setrlimit()\n"); syncLog();
        return -1;
    }
    /***********************************/

    logGW("Starting process %s.", procTag);
    logGW("LHOME: '%s'", LHOME);
    syncLog();

    /* read configuration file */
    getConfiguration();
   
    /*************/

    /* Verificamos que no se este ejecutando otra instancia en el proceso padre */ 
    if (lockPID(LHOME, procTag) != 0)
    {
        logGW("Couldn't lock pid file. Maybe another instance is running. Process [%d] aborted.", getpid());
        exit(-1);
    }  

    /* Become a daemon */
    switch (fork())
    {
        case 0:
            break;
        case -1:
            logGW("Unable to become a daemon");
        default:
            exit(0);
    };

    if (setsid () == -1)
    {
        perror("setsid failed.");
        exit(-1);
    }  
 
    /* Bloqueamos el archivo pid de esta instancia en el proceso hijo */ 
    sleep(1); 
    if (lockPID(LHOME, procTag) != 0)
    {
        logGW("Couldn't lock pid file. Maybe another instance is running. Process [%d] aborted.", getpid());
        exit(-1);
    }  

    /* Modificamos a _IOLBF (line buffered) los buffers STDOUT y STDERR */
    setlinebuf(stdout);
    setlinebuf(stderr);

    /* show configuration file */
    showConfig(&cfg);

    /* Start the dispatch GKV daemon */
    logGW("Daemon dispatchGKV started."); syncLog();

    /* Initialization */
    attachQDMT(&queueDMT, &cfg);
    httpInit();

    while (! checkProcStop())
    {
        int ret = processBatch();
        if (ret < 0)
        {
            logGW("Failed to process batch."); syncLog();
            sleep(cfg.dgk_read_wait);
        }
        else if (ret == 0)
        {
            if (time(NULL) - lastShown >= cfg.dgk_show_wait)
            {
                logGW("No rows processed."); syncLog();
                lastShown = time(NULL);
            }
            sleep(cfg.dgk_read_wait);
        }
        else
        {
            lastShown = time(NULL);
        }
    }

    logGW("Process stopped.");
    syncLog();

    /* Cleanup */
    httpFinish();

	/* Free resources */
	detachQDMT();
	
    return 0;
}

/* ######################## EOF ############################ */
