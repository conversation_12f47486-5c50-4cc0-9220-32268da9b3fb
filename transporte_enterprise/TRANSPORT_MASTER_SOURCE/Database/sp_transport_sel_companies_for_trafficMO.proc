USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_sel_companies_for_trafficMO;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_sel_companies_for_trafficMO`(p_limit INT)
BEGIN

    DECLARE l_utc_timestamp DATETIME DEFAULT NULL;
    
    SET l_utc_timestamp = UTC_TIMESTAMP();

    SELECT tr.id_company, cn.url_adapter_mo, count(*)
    FROM trafficMO tr, company_configuration cn
    WHERE tr.status IN ('QUEUED', 'FAILED')
    AND tr.deliverytime < l_utc_timestamp
    AND cn.id_company = tr.id_company
    AND cn.dispatch_status_mo = 'ACTIVE'    
    GROUP BY tr.id_company, cn.url_adapter_mo
    LIMIT p_limit;

END
$$

DELIMITER ;
