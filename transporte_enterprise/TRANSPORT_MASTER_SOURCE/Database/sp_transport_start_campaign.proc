USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_start_campaign;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_start_campaign`()
BEGIN

    START TRANSACTION;

    SELECT id_campaign, description_campaign, total_campaign
    FROM campaign
    WHERE date_start_campaign < UTC_TIMESTAMP()
    AND status = 'PENDING';

    UPDATE campaign 
    SET status = 'FORSEND'
    WHERE date_start_campaign < UTC_TIMESTAMP()
    AND status = 'PENDING';

    COMMIT;

END
$$

DELIMITER ;
