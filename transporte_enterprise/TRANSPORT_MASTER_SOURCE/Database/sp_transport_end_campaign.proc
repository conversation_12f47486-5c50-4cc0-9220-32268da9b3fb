USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_end_campaign;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_end_campaign`()
BEGIN

    DECLARE l_id_campaign INT DEFAULT NULL;

    START TRANSACTION;

    SELECT id_campaign INTO l_id_campaign
    FROM campaign
    WHERE status IN ('SENDING', 'PAUSED')
    AND (
            date_end_campaign < UTC_TIMESTAMP()

            OR (
                   NOT EXISTS (
                                  SELECT id
                                  FROM trafficMT FORCE INDEX (campaign)
                                  WHERE campaign = campaign.id_campaign
                                  AND deliveryTime IS NULL
                              )

                   AND

                   total_campaign = (
                                        SELECT COUNT(*)
                                        FROM trafficMT FORCE INDEX (campaign)
                                        WHERE campaign = campaign.id_campaign
                                        AND deliveryTime IS NOT NULL
                                    )
               )
        )
    LIMIT 1;

    IF (l_id_campaign IS NOT NULL) THEN

        UPDATE campaign
        SET status = 'FINISHED',
            date_finished = UTC_TIMESTAMP(),
            date_post_finished = NULL
        WHERE id_campaign = l_id_campaign;

        SELECT id_campaign, description_campaign, total_campaign
        FROM campaign
        WHERE id_campaign = l_id_campaign;

    ELSE

        SELECT id_campaign, description_campaign, total_campaign
        FROM campaign
        WHERE 1 = 2;

    END IF;

    COMMIT;

END$$
DELIMITER ;
