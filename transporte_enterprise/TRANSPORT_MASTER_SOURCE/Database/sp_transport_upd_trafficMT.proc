USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_upd_trafficMT;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_upd_trafficMT`(p_id INT, p_status VARCHAR(20), p_operator VARCHAR(30), p_errCode INT, p_errText VARCHAR(255), p_gkv_id VARCHAR(50))
BEGIN

    IF (p_status = 'DISPATCHED') THEN

        UPDATE trafficMT
        SET status = p_status,
            recipientDomain = IF(p_operator IS NULL, recipientDomain, p_operator),
            deliveryTime = UTC_TIMESTAMP(),
            deliveryCount = deliveryCount + 1,
            errCode = p_errCode,
            errText = p_errText,
            gkserial = p_gkv_id
        WHERE id = p_id;

    ELSE

        -- PUSHED NOPUSHED CONFIRMED FAILED 

        UPDATE trafficMT
        SET status = p_status,
            recipientDomain = IF(p_operator IS NULL, recipientDomain, p_operator),
            errCode = p_errCode,
            errText = p_errText
        WHERE id = p_id;

    END IF;

END
$$

DELIMITER ;
