USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_sel_companies_for_acknowledgeMT;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_sel_companies_for_acknowledgeMT`(p_limit INT)
BEGIN

    SELECT ack.id_company, cn.url_adapter_ack, count(*)
    FROM acknowledgeMT ack, company_configuration cn
    WHERE ack.status IN ('QUEUED', 'FAILED')
    AND ack.delivery_time < UTC_TIMESTAMP()
    AND cn.id_company = ack.id_company
    AND cn.dispatch_status_ack = 'ACTIVE'    
    GROUP BY ack.id_company, cn.url_adapter_ack
    LIMIT p_limit;

END
$$

DELIMITER ;
