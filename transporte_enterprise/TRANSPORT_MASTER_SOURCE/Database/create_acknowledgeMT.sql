
DROP TABLE IF EXISTS `acknowledgeMT`;

CREATE TABLE `acknowledgeMT` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `app_id` varchar(45) DEFAULT NULL,
  `msg_id` varchar(45) DEFAULT NULL,
  `id_gkv` int(11) unsigned DEFAULT NULL,
  `ref_mt_id` int(11) unsigned DEFAULT NULL,
  `id_company` int(11) unsigned DEFAULT NULL,
  `client_id` varchar(45) DEFAULT NULL,
  `phone` varchar(30) DEFAULT NULL,
  `short_number` varchar(30) DEFAULT NULL,
  `carrier` varchar(45) DEFAULT NULL,
  `ack_status` enum('PUSHED','NOPUSHED','EXPIRED','DELIVERED','UNDELIVERED') NOT NULL,
  `ack_reason` varchar(255) NOT NULL DEFAULT '',
  `status` enum('QUEUED','FAILED','EXPIRED','PULLED','DISCARDED') NOT NULL,
  `received_time` datetime NOT NULL,
  `dispatched_time` datetime DEFAULT NULL,
  `delivery_time` datetime NOT NULL,
  `delivery_count` int(11) NOT NULL DEFAULT '0',
  `delivery_code` int(11) NOT NULL DEFAULT '0',
  `delivery_msg` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `company_status_delivery_time` (`id_company`,`status`,`delivery_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
