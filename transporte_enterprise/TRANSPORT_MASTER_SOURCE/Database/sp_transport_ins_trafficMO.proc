USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_ins_trafficMO;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_ins_trafficMO`(
  p_gkv_token VARCHAR(45),
  p_operator VARCHAR(30),
  p_msisdn VARCHAR(30),
  p_message VARCHAR(255)
)
BEGIN

    DECLARE l_count INT DEFAULT 0;
    DECLARE l_company INT DEFAULT 0;
    DECLARE l_utc_timestamp DATETIME DEFAULT NULL;
    DECLARE l_url_adapter_mo VARCHAR(255) DEFAULT NULL;

    SELECT count(*), min(id_company) INTO l_count, l_company
    FROM company_configuration
    WHERE gkv_token = p_gkv_token;

    IF (l_count = 1) THEN

		SELECT url_adapter_mo INTO l_url_adapter_mo
		FROM company_configuration
		WHERE id_company = l_company;

		IF (l_url_adapter_mo IS NULL OR TRIM(l_url_adapter_mo) = '') THEN

			SET l_count = -1;

		ELSE

			SET l_utc_timestamp = UTC_TIMESTAMP();

			INSERT INTO trafficMO
			(
				id_company,
				carrier,
				phone,
				msgtext,
				status,
				receivedtime,
				dispatchtime,
				deliverytime,
				delivery_count,
				delivery_code,
				delivery_msg
			)
			VALUES
			(
				l_company,
				p_operator,
				p_msisdn,
				p_message,
				'QUEUED',
				l_utc_timestamp,
				NULL,
				l_utc_timestamp,
				0,
				0,
				''
			);

		END IF;

    END IF;

    SELECT l_count AS 'rows';

END
$$

DELIMITER ;
