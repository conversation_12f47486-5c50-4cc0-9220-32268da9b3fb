USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_sel_acknowledgeMT;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_sel_acknowledgeMT`(p_company INT, p_cola INT, p_modulo INT, p_limit INT)
BEGIN

    SELECT ack.id, ack.client_id, ack.phone, ack.ack_status, ack.ack_reason, ack.carrier, ack.received_time, ack.delivery_count, 
           cn.time_retry_ack, cn.max_retry_ack
    FROM acknowledgeMT ack FORCE INDEX (company_status_delivery_time), company_configuration cn
    WHERE ack.id_company = p_company
    AND ack.status IN ('QUEUED', 'FAILED')
    AND ack.delivery_time < UTC_TIMESTAMP()
    AND cn.id_company = ack.id_company
    AND cn.dispatch_status_ack = 'ACTIVE'
    AND MOD(ack.id, p_modulo) = p_cola
    ORDER BY ack.id
    LIMIT p_limit;

END
$$

DELIMITER ;
