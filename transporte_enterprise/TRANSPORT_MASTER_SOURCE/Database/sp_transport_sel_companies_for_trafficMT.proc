USE mcs_master_proyect;

DROP PROCEDURE IF EXISTS `sp_transport_sel_companies_for_trafficMT`;

DELIMITER $$
CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_sel_companies_for_trafficMT`(p_limit INT)
BEGIN

    DECLARE l_utc_timestamp DATETIME DEFAULT NULL;
    
    SET l_utc_timestamp = UTC_TIMESTAMP();

    SELECT tr.company, cn.gkv_token, count(*)
    FROM trafficMT tr FORCE INDEX (company_status_dispatch_time), company_configuration cn FORCE INDEX (PRIMARY)
    WHERE tr.company = cn.id_company
    AND tr.status = 'FORDISPATCH'
    AND tr.dispatchTime < l_utc_timestamp
    AND cn.dispatch_status_mt = 'ACTIVE'
    GROUP BY tr.company
    LIMIT p_limit;

END $$
DELIMITER ;

