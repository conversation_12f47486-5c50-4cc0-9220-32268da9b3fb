USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_upd_acknowledgeMT;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_upd_acknowledgeMT`(p_id INT, p_status VARCHAR(20), p_delta INT, p_errCode INT, p_errText VARCHAR(255))
BEGIN

    IF (p_status = 'PULLED') THEN

        UPDATE acknowledgeMT
        SET status = p_status,
            dispatched_time = UTC_TIMESTAMP(),
            delivery_count = delivery_count + 1,
            delivery_code = p_errCode,
            delivery_msg = p_errText
        WHERE id = p_id;

    ELSEIF (p_status = 'FAILED') THEN
    
        UPDATE acknowledgeMT
        SET status = p_status,
            dispatched_time = UTC_TIMESTAMP(),
            delivery_time = DATE_ADD(UTC_TIMESTAMP(), INTERVAL p_delta SECOND),
            delivery_count = delivery_count + 1,
            delivery_code = p_errCode,
            delivery_msg = p_errText
        WHERE id = p_id;
    
    ELSEIF (p_status = 'EXPIRED') THEN
    
        UPDATE acknowledgeMT
        SET status = p_status,
			dispatched_time = UTC_TIMESTAMP(),
            delivery_count = delivery_count + 1,
            delivery_code = p_errCode,
            delivery_msg = p_errText
        WHERE id = p_id;

    ELSE

        --  DISCARDED

        UPDATE acknowledgeMT
        SET status = p_status,
			dispatched_time = UTC_TIMESTAMP(),
            delivery_count = delivery_count + 1,
            delivery_code = p_errCode,
            delivery_msg = p_errText
        WHERE id = p_id;

    END IF;

END
$$

DELIMITER ;
