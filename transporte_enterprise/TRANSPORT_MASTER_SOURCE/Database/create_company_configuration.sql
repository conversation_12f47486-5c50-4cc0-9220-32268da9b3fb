DROP TABLE IF EXISTS `company_configuration`;

CREATE TABLE `company_configuration` (
  `id_company` int(11) unsigned NOT NULL,
  `gkv_token` varchar(45) NOT NULL,
  `ws_token_mt` varchar(255) DEFAULT NULL,
  `url_adapter_mo` varchar(255) DEFAULT NULL,
  `time_retry_mo` int(11) NOT NULL DEFAULT '60',
  `max_retry_mo` int(11) NOT NULL DEFAULT '5',
  `active_retry_mo` enum('Y','N') NOT NULL DEFAULT 'Y',
  `dispatch_status_mo` enum('ACTIVE','INACTIVE') DEFAULT 'ACTIVE',
  `dispatch_status_mt` enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'ACTIVE',
  `data_report_custom` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  `id_custom_report` int(11) NOT NULL DEFAULT '100',
  PRIMARY KEY (`id_company`),
  UNIQUE KEY `company_configuration_UN` (`ws_token_mt`),
  CONSTRAINT `company_configuration_company_FK` FOREIGN KEY (`id_company`) REFERENCES `company` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

----

alter table company_configuration change dispatch_status_mo dispatch_status_mo enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'INACTIVE';
alter table company_configuration change dispatch_status_mt dispatch_status_mt enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'INACTIVE';

alter table company_configuration add url_adapter_ack varchar(255) DEFAULT NULL after active_retry_mo;
alter table company_configuration add time_retry_ack int(11) NOT NULL DEFAULT '60' after url_adapter_ack;
alter table company_configuration add max_retry_ack int(11) NOT NULL DEFAULT '5' after time_retry_ack;
alter table company_configuration add active_retry_ack enum('Y','N') NOT NULL DEFAULT 'Y' after max_retry_ack;

alter table company_configuration add dispatch_status_ack enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'INACTIVE' after dispatch_status_mt;

insert into company_configuration (
  id_company,
  gkv_token,
  ws_token_mt,
  url_adapter_mo,
  time_retry_mo,
  max_retry_mo,
  active_retry_mo,
  dispatch_status_mo,
  dispatch_status_mt,
  data_report_custom,
  id_custom_report
)
select 
  id,
  gkv_token,
  ws_token_mt,
  url_adapter_mo,
  time_retry_mo,
  max_retry_mo,
  active_retry_mo,
  dispatch_status_mo,
  dispatch_status_mt,
  NULL,
  100
from company
order by id;

