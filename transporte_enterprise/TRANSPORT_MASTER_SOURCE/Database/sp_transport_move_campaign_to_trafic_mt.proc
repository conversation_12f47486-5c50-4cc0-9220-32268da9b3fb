USE mcs_master_proyect;

DEL<PERSON><PERSON>ER $$

DROP PROCEDURE IF EXISTS sp_transport_move_campaign_to_trafic_mt;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_move_campaign_to_trafic_mt`()
BEGIN

    DECLARE l_id_campaign INT DEFAULT NULL;
    DECLARE l_id_company INT DEFAULT NULL;
    DECLARE l_date_create_campaign DATETIME DEFAULT NULL;
    DECLARE l_login VARCHAR(30) DEFAULT NULL;

    

    START TRANSACTION;

    SELECT c.id_campaign, c.id_company, c.date_create_campaign, a.login
    INTO l_id_campaign, l_id_company, l_date_create_campaign, l_login
    FROM campaign c INNER JOIN account a ON a.id = c.id_account
    WHERE c.status = 'FORSEND'
    ORDER BY c.id_campaign ASC
    LIMIT 1;

    IF (l_id_campaign IS NOT NULL) THEN

        INSERT INTO trafficMT (
            recipientId,
            company,
            recipientDomain,
            status,
            login,
            msgText,
            receivedTime,
            dispatchTime,
            deliveryTime,
            deliveryCount,
            refMO,
            errCode,
            errText,
            input_process,
            input_mode,
            gkserial,
            campaign,
            wserial,
            querystring,
            extid,
            extstatus
        )
        SELECT
            number_detcamp,
            l_id_company,
            'UNKNOWN',
            'QUEUED',
            l_login,
            message_detcamp,
            l_date_create_campaign,
            UTC_TIMESTAMP(),
            NULL,
            0,
            0,
            0,
            '',
            0,
            'CAMPAIGN',
            0,
            l_id_campaign,
            NULL,
            NULL,
            NULL,
            NULL
        FROM detail_campaign
        WHERE id_campaign = l_id_campaign
        AND status_detcamp = 'PENDING'
        ORDER BY id_detcamp;

        UPDATE detail_campaign 
        SET status_detcamp = 'PROCESSED' 
        WHERE id_campaign = l_id_campaign;

        UPDATE campaign
        SET status = 'SENDING'
        WHERE id_campaign = l_id_campaign;

        SELECT id_campaign, description_campaign, total_campaign
        FROM campaign
        WHERE id_campaign = l_id_campaign;

    ELSE

        SELECT id_campaign, description_campaign, total_campaign
        FROM campaign
        WHERE 1 = 2;

    END IF;

    COMMIT;
    
END
$$

DELIMITER ;
