USE mcs_master_proyect;

DROP PROCEDURE IF EXISTS `sp_transport_sel_trafficMT`;

DELIMITER $$
CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_sel_trafficMT`(p_company INT, p_cola INT, p_modulo INT, p_limit INT)
BEGIN

    DECLARE l_utc_timestamp DATETIME DEFAULT NULL;

    DECLARE v_cant, v_part_limit INT;
    DECLARE v_id_campaign INT;
    DECLARE done INT DEFAULT FALSE;
    DECLARE cur_id_campaign CURSOR FOR SELECT id_campaign FROM tmp_list_campaign;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    DROP TEMPORARY TABLE IF EXISTS tmp_timecheck;
    DROP TEMPORARY TABLE IF EXISTS tmp_allowed_account;
    DROP TEMPORARY TABLE IF EXISTS tmp_list_campaign;
    DROP TEMPORARY TABLE IF EXISTS tmp_dispatch_mt;

    SET l_utc_timestamp = UTC_TIMESTAMP();

    CREATE TEMPORARY TABLE tmp_timecheck AS
    SELECT DISTINCT tz.offset AS offset,
           IF(fr.dia IS NOT NULL, 'FESTIVO', CASE WEEKDAY(DATE_ADD(l_utc_timestamp, INTERVAL tz.offset HOUR))
                                                 WHEN 0 THEN 'SEMANA'
                                                 WHEN 1 THEN 'SEMANA'
                                                 WHEN 2 THEN 'SEMANA'
                                                 WHEN 3 THEN 'SEMANA'
                                                 WHEN 4 THEN 'SEMANA'
                                                 WHEN 5 THEN 'SABADO'
                                                 WHEN 6 THEN 'DOMINGO'
                                             END) AS dayofweek
    FROM time_zone tz LEFT JOIN feriados fr
    ON fr.dia = DATE_FORMAT(DATE_ADD(l_utc_timestamp, INTERVAL tz.offset HOUR),'%d/%m') AND fr.active = 'Y';

    CREATE TEMPORARY TABLE tmp_allowed_account AS
    SELECT ac.id, ac.login
    FROM company_configuration cn, account ac, time_send_available ts, time_zone tz, tmp_timecheck tk
    WHERE cn.id_company = p_company
    AND cn.dispatch_status_mt = 'ACTIVE'
    AND ac.company = cn.id_company 
    AND ac.enabled = 'Y'
    AND ts.company = ac.company
    AND ts.active = 1
    AND ts.type = tk.dayofweek
    AND tk.offset = tz.offset
    AND tz.id_time_zone = ac.id_time_zone
    AND TIME(DATE_ADD(l_utc_timestamp, INTERVAL tz.offset HOUR)) BETWEEN ts.start AND ts.end;

    ALTER TABLE tmp_allowed_account ADD INDEX login_idx (login);

    CREATE TEMPORARY TABLE tmp_list_campaign AS
    SELECT DISTINCT cp.id_campaign
    FROM campaign cp, tmp_allowed_account aa
    WHERE cp.status = 'SENDING'
    AND cp.id_account = aa.id
    AND cp.id_company = p_company;

    SET v_cant = (SELECT COUNT(*) FROM tmp_list_campaign);
    IF (v_cant = 0) THEN

        SET v_part_limit = p_limit;

    ELSE

        SET v_part_limit = (SELECT FLOOR(p_limit / (v_cant +  1)));

    END IF;

    CREATE TEMPORARY TABLE tmp_dispatch_mt AS
    SELECT tr.id,
           tr.campaign AS id_campaign,
           tr.recipientId,
           tr.msgText,
           CONCAT('Company: ', tr.company, '  Login: ', tr.login) AS cc
    FROM trafficMT tr FORCE INDEX (company_status_id), tmp_allowed_account aa FORCE INDEX (login_idx)
    WHERE tr.company = p_company
    AND tr.status = 'FORDISPATCH'
    AND MOD(tr.id, p_modulo) = p_cola
    AND tr.dispatchTime < l_utc_timestamp
    AND (tr.campaign is null OR tr.campaign = 0)
    AND tr.login = aa.login
    ORDER BY tr.id
    LIMIT v_part_limit;

    IF (v_cant > 0) THEN

        SET v_part_limit = FLOOR((SELECT (p_limit - COUNT(*)) FROM tmp_dispatch_mt) / v_cant);

    END IF;


    OPEN cur_id_campaign;
    read_loop: LOOP

        FETCH cur_id_campaign INTO v_id_campaign;

        IF done THEN
            LEAVE read_loop;
        END IF;

        INSERT INTO tmp_dispatch_mt(
            id,
            id_campaign,
            recipientId,
            msgText,
            cc
        )
        SELECT id,
               campaign,
               recipientId,
               msgText,
               CONCAT('Company: ', company, '  Login: ', login) AS cc
        FROM trafficMT FORCE INDEX (campaign)
        WHERE campaign = v_id_campaign
        AND status = 'FORDISPATCH'
        AND dispatchTime < l_utc_timestamp
        AND MOD(id, p_modulo) = p_cola
        ORDER BY id
        LIMIT v_part_limit;

    END LOOP;

    CLOSE cur_id_campaign;

    SELECT id,
           recipientId,
           msgText,
           cc 
    FROM tmp_dispatch_mt
    ORDER BY id;
    
    
    DROP TEMPORARY TABLE tmp_timecheck;
    DROP TEMPORARY TABLE tmp_allowed_account;
    DROP TEMPORARY TABLE tmp_list_campaign;
    DROP TEMPORARY TABLE tmp_dispatch_mt;

END $$
DELIMITER ;

