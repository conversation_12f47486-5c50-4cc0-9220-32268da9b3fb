USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_post_end_campaign;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_post_end_campaign`(p_delta INT)
BEGIN

    DECLARE l_id_campaign INT DEFAULT NULL;
	DECLARE l_total_anulados INT DEFAULT 0;

    START TRANSACTION;

    SELECT id_campaign INTO l_id_campaign
    FROM campaign
    WHERE status = 'FINISHED'
    AND date_finished < DATE_ADD(UTC_TIMESTAMP(), INTERVAL -p_delta SECOND)
    AND date_post_finished IS NULL
    ORDER BY id_campaign
    LIMIT 1;

    IF (l_id_campaign IS NOT NULL) THEN

        UPDATE trafficMT FORCE INDEX (campaign)
        SET status = 'ANULADO',
            errCode = 101,
            errText = 'Se anula porque la campaña ha finalizado.'
        WHERE campaign = l_id_campaign
        AND status = 'FORDISPATCH';

		-- Obtener cantidad de anulados
		SET l_total_anulados = ROW_COUNT();

        UPDATE campaign
        SET date_post_finished = UTC_TIMESTAMP()
        WHERE id_campaign = l_id_campaign;

        SELECT id_campaign, description_campaign, total_campaign, l_total_anulados
        FROM campaign
        WHERE id_campaign = l_id_campaign;

    ELSE

        SELECT id_campaign, description_campaign, total_campaign, l_total_anulados
        FROM campaign
        WHERE 1 = 2;

    END IF;

    COMMIT;

END$$
DELIMITER ;
