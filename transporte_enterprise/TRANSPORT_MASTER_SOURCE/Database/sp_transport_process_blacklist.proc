USE mcs_master_proyect;

DROP PROCEDURE IF EXISTS `sp_transport_process_blacklist`;

DELIMITER $$
CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_process_blacklist`(v_queue INT, v_size INT, v_limit INT)
BEGIN

    DECLARE v_id INT DEFAULT NULL;
    DECLARE v_status VARCHAR(20) DEFAULT NULL;
    DECLARE v_id_company INT DEFAULT NULL;
    DECLARE done INT DEFAULT FALSE;
    DECLARE cur_traffic CURSOR FOR SELECT id, status, company FROM tmp_blacklist;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;


    DROP TEMPORARY TABLE IF EXISTS tmp_blacklist;

    CREATE TEMPORARY TABLE tmp_blacklist AS
    SELECT t.id, IF(b.number_blacklist IS NULL, 'FORDISPATCH', 'BLACKLISTED') AS status, t.company
    FROM trafficMT t FORCE INDEX (status_dispatch_time) LEFT JOIN blacklist b FORCE INDEX (fk_company_blacklist_idx)
    ON b.number_blacklist = t.recipientId AND b.id_company = t.company
    WHERE t.status = 'QUEUED'
    AND t.dispatchTime < UTC_TIMESTAMP()
    AND MOD(t.id, v_size) = v_queue
    ORDER BY t.dispatchTime ASC
    LIMIT v_limit;


    SET @total = 0;
    SET @blacklisted = 0;
    SET @for_dispatch = 0;
    
    OPEN cur_traffic;
    read_loop: LOOP

        FETCH cur_traffic INTO v_id, v_status, v_id_company;

        IF done THEN
            LEAVE read_loop;
        END IF;

        UPDATE trafficMT SET status = v_status WHERE id = v_id;
        
        IF (v_status = 'BLACKLISTED') THEN
			
			IF EXISTS (
					SELECT 1
					FROM company_configuration
					WHERE id_company = v_id_company
					AND dispatch_status_ack = 'ACTIVE'
			) THEN

				INSERT INTO acknowledgeMT
				(
					app_id,
					msg_id,
					id_gkv,
					ref_mt_id,
					id_company,
					client_id,
					phone,
					short_number,
					carrier,
					ack_status,
					ack_reason,
					status,
					received_time,
					dispatched_time,
					delivery_time,
					delivery_count,
					delivery_code,
					delivery_msg
				)
				SELECT
					NULL,
					NULL,
					NULL,
					id,
					company,
					extid,
					recipientId,
					NULL,
					NULL,
					'NOPUSHED',
					'Destination msisdn was blacklisted.',
					'QUEUED',
					UTC_TIMESTAMP(),
					NULL,
					UTC_TIMESTAMP(),
					0,
					0,
					''
				FROM trafficMT
				WHERE id = v_id;

			END IF;

        END IF;


        SET @total = @total + 1;
        SET @blacklisted = IF(v_status = 'BLACKLISTED', @blacklisted + 1, @blacklisted);
        SET @for_dispatch = IF(v_status = 'FORDISPATCH', @for_dispatch + 1, @for_dispatch);

    END LOOP;

    CLOSE cur_traffic;

    SELECT @total AS total,
           @blacklisted AS blacklisted,
           @for_dispatch AS for_dispatch;



    DROP TEMPORARY TABLE tmp_blacklist;

END $$
DELIMITER ;

