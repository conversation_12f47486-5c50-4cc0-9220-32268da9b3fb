
DROP TRIGGER `mcs_master_proyect`.`blacklist_AFTER_INSERT`;

DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`sqlmobid`@`%`*/ /*!50003 TRIGGER `mcs_master_proyect`.`blacklist_AFTER_INSERT`
AFTER INSERT ON `mcs_master_proyect`.`blacklist`
FOR EACH ROW
BEGIN 
	INSERT INTO blacklist_history
	(
		id_account,
		id_company,
		number_blkhist,
		detail_blkhist,
		date_blkhist,
		action
	)VALUES(
		NEW.id_account,
		NEW.id_company,
		NEW.number_blacklist,
		NEW.detail_blacklist,
		UTC_TIMESTAMP(),
		'INSERT'
	);
END */;;
DELIMITER ;

DROP TRIGGER `mcs_master_proyect`.`blacklist_BEFORE_DELETE`;

DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`sqlmobid`@`%`*/ /*!50003 TRIGGER `mcs_master_proyect`.`blacklist_BEFORE_DELETE`
BEFORE DELETE ON `mcs_master_proyect`.`blacklist`
FOR EACH ROW
BEGIN 
	INSERT INTO blacklist_history
	(
		id_account,
		id_company,
		number_blkhist,
		detail_blkhist,
		date_blkhist,
		action
	)VALUES(
		OLD.id_account,
		OLD.id_company,
		OLD.number_blacklist,
		OLD.detail_blacklist,
		UTC_TIMESTAMP(),
		'DELETE'
	);
END */;;
DELIMITER ;

