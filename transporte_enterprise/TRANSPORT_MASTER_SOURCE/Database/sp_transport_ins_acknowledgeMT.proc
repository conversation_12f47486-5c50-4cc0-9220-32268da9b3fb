USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_ins_acknowledgeMT;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_ins_acknowledgeMT`(
	p_app_id VARCHAR(45),
	p_msg_id VARCHAR(45),
	p_id_gkv INT,
	p_ref_mt_id INT,
	p_phone VARCHAR(30),
	p_short_number VARCHAR(30),
	p_carrier VARCHAR(45),
	p_ack_status VARCHAR(20),
	p_ack_reason VARCHAR(255)
)
BEGIN

	DECLARE l_ack_id INT DEFAULT 0;
	DECLARE l_id_company INT DEFAULT NULL;
	DECLARE l_client_id VARCHAR(45) DEFAULT NULL;
	DECLARE l_url_adapter_ack VARCHAR(255) DEFAULT NULL;

    SELECT company, extid INTO l_id_company, l_client_id
    FROM trafficMT
    WHERE id = p_ref_mt_id;
    
    IF (l_id_company IS NULL) THEN

		SET l_ack_id = 0;

    ELSE

		SELECT url_adapter_ack INTO l_url_adapter_ack
		FROM company_configuration
		WHERE id_company = l_id_company;

		IF (l_url_adapter_ack IS NULL OR TRIM(l_url_adapter_ack) = '') THEN
		
			SET l_ack_id = -1;
		
		ELSE

			INSERT INTO acknowledgeMT
			(
				app_id,
				msg_id,
				id_gkv,
				ref_mt_id,
				id_company,
				client_id,
				phone,
				short_number,
				carrier,
				ack_status,
				ack_reason,
				status,
				received_time,
				dispatched_time,
				delivery_time,
				delivery_count,
				delivery_code,
				delivery_msg
			)
			VALUES
			(
				p_app_id,
				p_msg_id,
				p_id_gkv,
				p_ref_mt_id,
				l_id_company,
				l_client_id,
				p_phone,
				p_short_number,
				p_carrier,
				p_ack_status,
				p_ack_reason,
				'QUEUED',
				UTC_TIMESTAMP(),
				NULL,
				UTC_TIMESTAMP(),
				0,
				0,
				''
			);

			SET l_ack_id = LAST_INSERT_ID();

		END IF;

    END IF;
    
    SELECT l_ack_id AS 'ack_id';
	
END
$$

DELIMITER ;
