USE mcs_master_proyect;

DELIMITER $$

DROP PROCEDURE IF EXISTS sp_transport_sel_trafficMO;

CREATE DEFINER=`sqlmobid`@`localhost` PROCEDURE `sp_transport_sel_trafficMO`(p_company INT, p_cola INT, p_modulo INT, p_limit INT)
BEGIN

    SELECT mo.id, mo.id_company, mo.carrier, mo.phone, mo.msgtext, mo.delivery_count, mo.status, 
           cn.time_retry_mo, cn.max_retry_mo
    FROM trafficMO mo FORCE INDEX (status), company_configuration cn
    WHERE mo.status IN ('QUEUED', 'FAILED')
    AND mo.deliverytime < UTC_TIMESTAMP()
    AND mo.id_company = p_company
    AND cn.id_company = mo.id_company
    AND cn.dispatch_status_mo = 'ACTIVE'
    AND MOD(mo.id, p_modulo) = p_cola
    ORDER BY mo.id
    LIMIT p_limit;

END
$$

DELIMITER ;
