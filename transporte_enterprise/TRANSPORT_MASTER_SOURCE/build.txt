#docker run --add-host="db1:**************" gkv/dispatch_ack    
docker build --platform linux/amd64 -t gkv/dispatch_gkv -f Dockerfile.DispatchGKV . 
docker build --platform linux/amd64 -t gkv/dispatch_ack -f Dockerfile.DispatchACK . 
docker build --platform linux/amd64 -t gkv/dispatch_mo -f Dockerfile.DispatchMO . 
docker build --platform linux/amd64 -t gkv/listen_gkv -f Dockerfile.ListenGKV . 
docker build --platform linux/amd64 -t gkv/maintenance_gkv -f Dockerfile.Maintenance . 
