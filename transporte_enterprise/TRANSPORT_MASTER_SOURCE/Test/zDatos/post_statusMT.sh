#   http://**********:8080/adapter-saesa/api/hookmt/status \

curl -X POST \
   http://localhost:8080/TEST/AcknowledgeMT \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Basic d2Vic2VydmljZToxMjM0NTY=' \
  -d '{
    "detailReqEntStatusMt": [
        {
            "id": "1",
            "clientCode": "11",
            "mobileNumber": "56912345678",
            "status": "PUSHED",
            "message": "hola como estas",
            "carrier": "MOVISTAR",
            "date": "2018-12-31 23:59:59"
        },
        {
            "id": "2",
            "clientCode": "22",
            "mobileNumber": "56912345678",
            "status": "NOPUSHED",
            "message": "hola como estas 2",
            "carrier": "CLARO",
            "date": "2018-12-31 23:59:59"
        }
    ]
}'

