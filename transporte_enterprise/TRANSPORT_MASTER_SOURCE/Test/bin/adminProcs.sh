#! /bin/sh

###############################################################

export LHOME=/home/<USER>/Desa/MASTER_PROYECT_MCS/process/TRANSPORT_MASTER_SOURCE/Test

APP=ENT

###############################################################

if [ ! -d "$LHOME" ]; then
  echo "Home directory not found: '$LHOME'"
  exit
fi

cd $LHOME/bin

USAGE="Use: $0 <start_all|stop_all|start_lgk|stop_lgk|start_dgk|stop_dgk|start_dmo|stop_dmo|star_dack|stop_dack|start_msd|stop_msd>"

ARG=$1

if [ -z $ARG ]; then
  echo "Missing init parameter !"
  echo "$USAGE"
  exit
fi

DONE="NO"

if [ $ARG = "start_lgk" -o $ARG = "start_all" ]; then

  TAG=LISTEN_GKV_$APP
  rm -f $LHOME/run/$TAG.cmd

  ./listenGKV $TAG 1>> ../run/$TAG.run 2>> ../run/$TAG.run
  if [ $? != 0 ]; then

    echo ""
    echo "Can't start listenGKV process. Maybe it's already running."
    echo ""

    exit

  fi

  sleep 2

  echo ""
  echo "Start lgk ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "stop_lgk" -o $ARG = "stop_all" ]; then

  TAG=LISTEN_GKV_$APP
  > $LHOME/run/$TAG.cmd

  echo ""
  echo "Sending stop command to lgk process ..."
  sleep 2
  echo "ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "start_dgk" -o $ARG = "start_all" ]; then

  TAG=DISPATCH_GKV_$APP
  rm -f $LHOME/run/$TAG.cmd

  ./dispatchGKV $TAG 1>> ../run/$TAG.run 2>> ../run/$TAG.run
  if [ $? != 0 ]; then

    echo ""
    echo "Can't start dispatchGKV process. Maybe it's already running."
    echo ""

    exit

  fi

  sleep 2

  echo ""
  echo "Start dgk ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "stop_dgk" -o $ARG = "stop_all" ]; then

  TAG=DISPATCH_GKV_$APP
  > $LHOME/run/$TAG.cmd

  echo ""
  echo "Sending stop command to dgk process ..."
  sleep 2
  echo "ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "start_dmo" -o $ARG = "start_all" ]; then

  TAG=DISPATCH_MO_$APP
  rm -f $LHOME/run/$TAG.cmd

  ./dispatchMO $TAG 1>> ../run/$TAG.run 2>> ../run/$TAG.run
  if [ $? != 0 ]; then

    echo ""
    echo "Can't start dispatchMO process. Maybe it's already running."
    echo ""

    exit

  fi

  sleep 2

  echo ""
  echo "Start dmo ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "stop_dmo" -o $ARG = "stop_all" ]; then

  TAG=DISPATCH_MO_$APP
  > $LHOME/run/$TAG.cmd

  echo ""
  echo "Sending stop command to dmo process ..."
  sleep 2
  echo "ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "start_dack" -o $ARG = "start_all" ]; then

  TAG=DISPATCH_ACK_$APP
  rm -f $LHOME/run/$TAG.cmd

  ./dispatchACK $TAG 1>> ../run/$TAG.run 2>> ../run/$TAG.run
  if [ $? != 0 ]; then

    echo ""
    echo "Can't start dispatchACK process. Maybe it's already running."
    echo ""

    exit

  fi

  sleep 2

  echo ""
  echo "Start dack ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "stop_dack" -o $ARG = "stop_all" ]; then

  TAG=DISPATCH_ACK_$APP
  > $LHOME/run/$TAG.cmd

  echo ""
  echo "Sending stop command to dack process ..."
  sleep 2
  echo "ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "start_msd" -o $ARG = "start_all" ]; then

  TAG=MAINTENANCE_SRV_$APP
  rm -f $LHOME/run/$TAG.cmd

  ./maintenanceSRV $TAG 1>> ../run/$TAG.run 2>> ../run/$TAG.run
  if [ $? != 0 ]; then

    echo ""
    echo "Can't start maintenanceSRV process. Maybe it's already running."
    echo ""

    exit

  fi

  sleep 2

  echo ""
  echo "Start msd ok."
  echo ""

  DONE="OK"

fi

if [ $ARG = "stop_msd" -o $ARG = "stop_all" ]; then

  TAG=MAINTENANCE_SRV_$APP
  > $LHOME/run/$TAG.cmd

  echo ""
  echo "Sending stop command to msd process ..."
  sleep 2
  echo "ok."
  echo ""

  DONE="OK"

fi

if [ ! $DONE = "OK" ]; then
  echo "Invalid parameter ($ARG) !"
  echo "$USAGE"
fi


