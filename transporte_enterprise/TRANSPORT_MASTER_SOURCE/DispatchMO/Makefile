# Makefile for server dispatchMO

CC         = clang
CCFLAGS    = -ggdb -O -Wall -Wno-pointer-sign -ggdb -D_REENTRANT -D_FILE_OFFSET_BITS=64
CCINCLUDES = -I../Common
LD         = $(CC)
LDLIBS     = -lpthread -lcurl -lm -L/usr/lib64/mysql/ -lmysqlclient

BIN        = ./dispatchMO
OBJ        = main.o cJSON.o service.o

all:	dispatchMO

main.o: main.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c main.c

cJSON.o: cJSON.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c cJSON.c

service.o: ../Common/service.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c ../Common/service.c

dispatchMO: $(OBJ)
	$(LD) -o $@ main.o cJSON.o service.o $(LDFLAGS) $(LDLIBS)
	strip dispatchMO

clean:
	rm -f core *.o $(BIN)
