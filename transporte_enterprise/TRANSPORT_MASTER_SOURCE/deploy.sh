#! /bin/sh

HOME=$(pdw)
BIN=$HOME/Test/bin

clear

echo ""
echo "Compilacion de binarios para el servicio 'Transporte Enterprise' de Mobid"
date
echo ""

echo ""
echo "Generando DispatchGKV ..."
cd $HOME/DispatchGKV
make clean; make
cp dispatchGKV $BIN/
echo "OK."
echo ""

echo ""
echo "Generando DispatchMO ..."
cd $HOME/DispatchMO
make clean; make
cp dispatchMO $BIN/
echo "OK."
echo ""

echo ""
echo "Generando DispatchACK ..."
cd $HOME/DispatchACK
make clean; make
cp dispatchACK $BIN/
echo "OK."
echo ""

echo "Generando ListenGKV ..."
cd $HOME/ListenGKV
make clean; make
cp listenGKV $BIN/
echo "OK."
echo ""

echo "Generando Maintenance ..."
cd $HOME/Maintenance
make clean; make
cp maintenanceSRV $BIN/
echo "OK."
echo ""

echo "DONE"
echo ""

