# Makefile for maintenanceSRV

CC         = clang
CCFLAGS    = -O -Wall -Wno-pointer-sign -ggdb -D_REENTRANT -D_FILE_OFFSET_BITS=64
CCINCLUDES = -I../Common
LDLIB      = -L/usr/lib64/mysql/ -lmysqlclient

BIN        = ./maintenanceSRV
OBJ        = main.o service.o

all:	maintenanceSRV

main.o: main.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c main.c

service.o: ../Common/service.c
	$(CC) $(CCFLAGS) $(CCINCLUDES) -c ../Common/service.c

maintenanceSRV: $(OBJ)
	$(CC) $(OBJ) -o $(BIN) $(LDLIB)
	strip maintenanceSRV

clean:
	rm -f core *.o $(BIN)
	

