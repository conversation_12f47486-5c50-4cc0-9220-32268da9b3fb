#include <unistd.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <sys/wait.h>
#include <service.h>
#include <dirent.h>

transport_cfg cfg;

char srvTagL[80 + 1];
time_t lastShown = 0;

char* LHOME = NULL;

/**********************************************/

// TODO
// Trap signals

// MHL
// 2018-09-10
// Revisar los registros de la trafficMT que pudiesen
// estar pendientes para las campañas terminadas por tiempo.

/**********************************************/

void getConfiguration()
{
    char cfgFile[100 + 1];

    FILE *fd = NULL;

    sprintf(cfgFile, "%s/conf/params.cfg", LHOME);

    if ((fd = fopen(cfgFile, "r")) != NULL)
    {
        readConfigFile(fd, &cfg);
        fclose(fd);
    }
    else
    {
        logGW("Can't open config file: %s", cfgFile);
        logGW("Force exit!");
        exit(-1);
    }
}

int checkProcStop()
{
    char ctlFile[100 + 1];

    FILE *fd = NULL;

    sprintf(ctlFile, "%s/run/%s.cmd", LHOME, srvTagL);

    if ((fd = fopen(ctlFile, "r")) != NULL)
    {
        fclose(fd);
        return 1;
    }
    else
    {
        return 0;
    }
}

int processBatch()
{
	int total = 0;
	
    /*
     *  check black list
     */
    total += checkBlackList(cfg.msd_batch_size, cfg.msd_sp_blacklist);
    
    /*
     *  start campaign
     */
    total += startCampaign(cfg.msd_sp_start_campaign);
    
    /*
     *  move campaign
     */
    total += moveCampaign(cfg.msd_sp_move_campaign);
    
    /*
     *  end campaign
     */
    total += endCampaign(cfg.msd_sp_end_campaign);

    /*
     *  post end campaign
     */
    total += postCampaign(cfg.msd_post_time , cfg.msd_sp_post_campaign);

    return total;
}

/**********************************************/

int main(int argc, char **argv)
{
    LHOME = getenv("LHOME");

    if (LHOME == NULL)
    {
        printf("Undefined LHOME environment variable.\n");
        //exit(-1);
        LHOME="/home/<USER>/GKV/";
    }

    DIR* dir = opendir(LHOME);
    if (dir)
    {  
         closedir(dir);
    }
    else   
    {
        printf("Invalid LHOME environment variable. Directory does not exists: %s\n", LHOME);
        exit(-1);
    }  
       
    if (argc != 2)
    {
        printf("Uso: %s <tag>\n", argv[0]);
        printf("Invalid argument.\n");
        exit(-1);
    }

    strcpy(srvTagL, argv[1]);

    initLog(LHOME, srvTagL);

    /***********************************/
    /* Genera un core si se requiere */
    struct rlimit r;

    r.rlim_cur = RLIM_INFINITY;
    r.rlim_max = RLIM_INFINITY;
    if (setrlimit(RLIMIT_CORE, &r) == -1)
    {
        logGW("Error in setrlimit()\n"); syncLog();
        return -1;
    }
    /***********************************/

    logGW("Starting process %s.", srvTagL);
    logGW("LHOME: '%s'", LHOME);
    syncLog();

    /* read configuration file */
    getConfiguration();

    /*************/

    /* Verificamos que no se este ejecutando otra instancia en el proceso padre */ 
    if (lockPID(LHOME, srvTagL) != 0)
    {
        logGW("Couldn't lock pid file. Maybe another instance is running. Process [%d] aborted.", getpid());
        exit(-1);
    }  
#ifdef __STAND_ALONE__
    /* Become a daemon */
    switch (fork())
    {
        case 0:
            break;
        case -1:
            logGW("Unable to become a daemon");
        default:
            exit(0);
    };

    if (setsid () == -1)
    {
        perror("setsid failed.");
        exit(-1);
    }  
 
    /* Bloqueamos el archivo pid de esta instancia en el proceso hijo */ 
    sleep(1); 
    if (lockPID(LHOME, srvTagL) != 0)
    {
        logGW("Couldn't lock pid file. Maybe another instance is running. Process [%d] aborted.", getpid());
        exit(-1);
    }  
#endif
    /* Modificamos a _IOLBF (line buffered) los buffers STDOUT y STDERR */
    setlinebuf(stdout);
    setlinebuf(stderr);

    /* show configuration file */
    showConfig(&cfg);

    /* Start the maintenanceSRV daemon */
    logGW("Daemon maintenanceSRV started."); syncLog();

    /* Initialization */
    attachQMSD(&cfg);

    while (! checkProcStop())
    {
        if (processBatch() == 0)
        {
            if (time(NULL) - lastShown >= cfg.msd_show_wait)
            {
                logGW("No actions pendings."); syncLog();
                lastShown = time(NULL);
            }  
            sleep(cfg.msd_read_wait);
        }  
        else
        {
            lastShown = time(NULL);
        }
    }

    logGW("Process stopped.");
    syncLog();

	/* Free resources */
	detachQMSD();

    return 0;
}

/* ######################## EOF ############################ */
