# Dockerfile específico para ListenGKV con health check
FROM alpine:3.18 AS builder

LABEL description="Builder container - ListenGKV"

RUN apk update && apk add --no-cache \ 
    autoconf binutils build-base clang curl curl-dev libtool libxslt-dev \ 
    linux-headers lz4-dev make musl-dev mysql-dev zlib-dev zstd-dev

RUN mkdir -p /Desa/ListenGKV && mkdir -p /Desa/Common 
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Common /Desa/Common
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/ListenGKV /Desa/ListenGKV
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Test/conf/params.cfg  /Desa/ListenGKV/ 

RUN cd /Desa/ListenGKV && make clean all

############################
# STEP 2: Build runtime image
############################
FROM alpine:3.18

LABEL description="Run container - ListenGKV with health check"

RUN apk update && apk add --no-cache \
    bash curl libcurl mariadb-connector-c net-tools nginx tzdata && \
    mkdir -p /home/<USER>/GKV/bin \
             /home/<USER>/GKV/cfg \
             /home/<USER>/GKV/run \
             /home/<USER>/GKV/conf \
             /home/<USER>/GKV/log \
             /var/www/html && \
    addgroup -S apptiaxa && \
    adduser -S apptiaxa -G apptiaxa && \
    chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

ENV TZ=America/Santiago
ENV SERVICE_NAME=ListenGKV
ENV SERVICE_PORT=3741
ENV SERVICE_BINARY=listenGKV

# Copy the compiled binary
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/ListenGKV/listenGKV /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/ListenGKV/params.cfg /home/<USER>/GKV/conf/

# Copy nginx configuration
COPY nginx-gkv.config /etc/nginx/http.d/default.conf

# Create initial health check file
RUN echo "OK" > /var/www/html/health && \
    echo "Service: ListenGKV" >> /var/www/html/health

# Copy startup script
COPY start-gkv.sh /start-gkv.sh
RUN chmod +x /start-gkv.sh

# Expose both service port and nginx port
EXPOSE 3741 80

WORKDIR /home/<USER>/GKV/bin

# Health check for AWS ECS/ELB
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Use the startup script as entrypoint
CMD ["/start-gkv.sh"]
