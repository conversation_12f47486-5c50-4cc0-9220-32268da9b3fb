# Migración del Pipeline de Java a C

## Resumen de Cambios

Este documento describe los cambios realizados para migrar el pipeline de Bitbucket de compilación Java/Maven a compilación de código C para los servicios de transporte GKV.

## Cambios Principales

### 1. Herramientas de Compilación
- **Antes**: <PERSON><PERSON> (`mvn clean install`, `mvn package`)
- **Después**: Clang + Make (`make clean all`)
- **Dependencias**: 
  - `build-essential clang make curl libcurl4-openssl-dev libmysqlclient-dev`

### 2. Estructura del Proyecto
- **Ruta de fuentes**: `transporte_enterprise/TRANSPORT_MASTER_SOURCE/`
- **Servicios disponibles**:
  - `DispatchACK`
  - `DispatchGKV` 
  - `DispatchMO`
  - `ListenGKV`
  - `Maintenance`

### 3. Pipelines Actualizados

#### Build Pipelines:
- `build_dispatch_ack` → Construye gkv-dispatch-ack
- `build_dispatch_gkv` → Construye gkv-dispatch-gkv  
- `build_dispatch_mo` → Construye gkv-dispatch-mo
- `build_listen_gkv` → Construye transporte-gkv-listengkv
- `build_maintenance` → Construye gkv-maintenance

#### Update Pipelines:
- `update_dispatch-ack`
- `update_dispatch-gkv`
- `update_dispatch-mo` 
- `update_listen-gkv`
- `update_maintenance`

### 4. Dockerfile Multi-servicio
Se creó un `Dockerfile` principal que puede construir cualquier servicio usando el argumento `SERVICE_NAME`:

```bash
docker build --build-arg SERVICE_NAME=DispatchACK -t gkv-dispatch-ack .
```

### 5. Proceso de Compilación

1. **Instalación de dependencias**: Clang, Make, librerías de desarrollo
2. **Configuración de certificados**: Se mantiene igual (SSL certificates)
3. **Compilación**: 
   ```bash
   cd transporte_enterprise/TRANSPORT_MASTER_SOURCE/${SERVICE_NAME}
   make clean all
   ```
4. **Construcción de imagen Docker**: Con argumento SERVICE_NAME
5. **Push a ECR**: Se mantiene igual

### 6. Variables de Entorno

Las siguientes variables se mantienen:
- `IMAGE_NAME`: Nombre de la imagen Docker
- `APP_FOLDER`/`API_FOLDER`: Nombre del servicio a compilar
- `SEMVER`: Versión semántica generada
- Certificados SSL: `CA_CERT_B64`, `CLIENT_CERT_B64`, `CLIENT_KEY_B64`

## Uso del Pipeline

### Para ejecutar un build:
1. Ir a Bitbucket Pipelines
2. Seleccionar "Custom" 
3. Elegir el pipeline deseado (ej: `build_dispatch_ack`)
4. Ejecutar

### Para hacer un update/deploy:
1. Seleccionar el pipeline de update correspondiente
2. Configurar variables de entorno (`installation`, `env`)
3. Ejecutar

## Archivos Modificados

- `bitbucket-pipelines.yml`: Pipeline principal actualizado
- `Dockerfile`: Nuevo Dockerfile multi-servicio
- `PIPELINE_C_MIGRATION.md`: Esta documentación

## Notas Importantes

1. **Makefiles**: Cada servicio tiene su propio Makefile optimizado
2. **Dependencias**: Los servicios dependen de MySQL client y libcurl
3. **Certificados**: El manejo de certificados SSL se mantiene igual
4. **Cache**: Se cambió de Maven cache a Docker cache
5. **Compatibilidad**: El flujo general se mantiene igual para facilitar la transición

## Próximos Pasos

1. Verificar que todos los Makefiles compilen correctamente
2. Probar los pipelines en un entorno de desarrollo
3. Actualizar los archivos de configuración de ECS task definitions
4. Documentar cualquier configuración específica por servicio
