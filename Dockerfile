# Multi-stage Dockerfile for GKV Transport Services
# This Dockerfile can build any of the C services based on the SERVICE_NAME build arg

# STEP 1: Build executable binary
############################
FROM alpine:latest AS builder

ARG SERVICE_NAME
LABEL description="Builder container - ${SERVICE_NAME}"

RUN apk update && apk add --no-cache \ 
    autoconf build-base binutils curl clang libtool libxslt-dev linux-headers make musl-dev \ 
    zlib-dev zstd-dev lz4-dev mysql-dev curl-dev

RUN mkdir -p /Desa/${SERVICE_NAME} && mkdir -p /Desa/Common 
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Common /Desa/Common
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/${SERVICE_NAME} /Desa/${SERVICE_NAME}
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Test/conf/params.cfg  /Desa/${SERVICE_NAME}/ 

RUN cd /Desa/${SERVICE_NAME} && make clean all

############################
# STEP 2: Build a small image
############################
FROM alpine:latest

ARG SERVICE_NAME
LABEL description="Run container - ${SERVICE_NAME}"

RUN apk update && apk add --no-cache tzdata\ 
    mariadb-connector-c libcurl
ENV TZ=America/Santiago

RUN mkdir -p /home/<USER>/GKV/bin
RUN mkdir -p /home/<USER>/GKV/cfg
RUN mkdir -p /home/<USER>/GKV/run
RUN mkdir -p /home/<USER>/GKV/conf
RUN mkdir -p /home/<USER>/GKV/log

RUN addgroup -S apptiaxa && adduser -S apptiaxa -G apptiaxa
RUN chown -R apptiaxa:apptiaxa /home/<USER>/GKV/
USER apptiaxa

# Copy the compiled binary (name varies by service)
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/${SERVICE_NAME}/* /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/${SERVICE_NAME}/params.cfg /home/<USER>/GKV/conf/

WORKDIR /home/<USER>/GKV/bin

# Default port (can be overridden)
EXPOSE 3738

# The entrypoint will be set based on the service being built
# This will need to be customized per service in the pipeline
