#!/bin/bash

# Variables de entorno
SERVICE_NAME=${SERVICE_NAME:-"unknown"}
SERVICE_PORT=${SERVICE_PORT:-3738}
SERVICE_BINARY=${SERVICE_BINARY:-""}

echo "=== Iniciando servicio GKV: $SERVICE_NAME ==="
echo "Puerto: $SERVICE_PORT"
echo "Binario: $SERVICE_BINARY"

# Verificar que el binario existe
if [ ! -f "/home/<USER>/GKV/bin/$SERVICE_BINARY" ]; then
    echo "ERROR: Binario $SERVICE_BINARY no encontrado en /home/<USER>/GKV/bin/"
    exit 1
fi

# Crear archivo de health check estático
echo "OK" > /var/www/html/health
echo "Service: $SERVICE_NAME" >> /var/www/html/health
echo "Status: Running" >> /var/www/html/health
echo "Port: $SERVICE_PORT" >> /var/www/html/health
echo "Timestamp: $(date)" >> /var/www/html/health

# Función para verificar si el servicio está corriendo
check_service() {
    local retries=0
    local max_retries=30
    
    while [ $retries -lt $max_retries ]; do
        if netstat -tuln | grep -q ":$SERVICE_PORT "; then
            echo "Servicio $SERVICE_NAME está corriendo en puerto $SERVICE_PORT"
            return 0
        fi
        echo "Esperando que el servicio $SERVICE_NAME inicie... (intento $((retries+1))/$max_retries)"
        sleep 2
        retries=$((retries+1))
    done
    
    echo "ERROR: El servicio $SERVICE_NAME no pudo iniciarse en el puerto $SERVICE_PORT"
    return 1
}

# Cambiar al directorio de trabajo del servicio
cd /home/<USER>/GKV/bin

# Iniciar el servicio C en segundo plano
echo "Iniciando servicio C: $SERVICE_BINARY"
case $SERVICE_NAME in
    "DispatchACK")
        ./$SERVICE_BINARY ACK &
        ;;
    "DispatchGKV")
        ./$SERVICE_BINARY GKV &
        ;;
    "DispatchMO")
        ./$SERVICE_BINARY MO &
        ;;
    "ListenGKV")
        ./$SERVICE_BINARY &
        ;;
    "Maintenance")
        ./$SERVICE_BINARY &
        ;;
    *)
        echo "Servicio desconocido: $SERVICE_NAME"
        ./$SERVICE_BINARY &
        ;;
esac

SERVICE_PID=$!
echo "Servicio iniciado con PID: $SERVICE_PID"

# Verificar que el servicio esté corriendo
if ! check_service; then
    echo "Fallo al iniciar el servicio, terminando..."
    exit 1
fi

# Función para manejar señales de terminación
cleanup() {
    echo "Recibida señal de terminación, cerrando servicios..."
    kill $SERVICE_PID 2>/dev/null
    nginx -s quit 2>/dev/null
    exit 0
}

trap cleanup SIGTERM SIGINT

# Iniciar nginx en primer plano
echo "Iniciando nginx..."
nginx -g 'daemon off;' &
NGINX_PID=$!

# Monitorear ambos procesos
while true; do
    # Verificar si el servicio C sigue corriendo
    if ! kill -0 $SERVICE_PID 2>/dev/null; then
        echo "ERROR: El servicio C ha terminado inesperadamente"
        kill $NGINX_PID 2>/dev/null
        exit 1
    fi
    
    # Verificar si nginx sigue corriendo
    if ! kill -0 $NGINX_PID 2>/dev/null; then
        echo "ERROR: Nginx ha terminado inesperadamente"
        kill $SERVICE_PID 2>/dev/null
        exit 1
    fi
    
    sleep 5
done
