# Template docker-push
# This template allows you to build and push your docker image to a Docker Hub account.
# The workflow allows running tests, code linting and security scans on feature branches (as well as master).
# The docker image will be validated and pushed to the docker registry after the code is merged to master.
# Prerequisites: $DOCKERHUB_USERNAME, $DOCKERHUB_PASSWORD setup as deployment variables

image: atlassian/default-image:2
clone:
  depth: full
definitions:
  build: &build_push
    name: build image
    oidc: true
    script:
      - export AWS_WEB_IDENTITY_TOKEN_FILE="/tmp/aws-web-identity-token-file"
      - echo ${BITBUCKET_STEP_OIDC_TOKEN} > ${AWS_WEB_IDENTITY_TOKEN_FILE}
      - test -f dotenv && source dotenv
      - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      - unzip awscliv2.zip
      - ./aws/install
      - eval $(aws sts assume-role --role-arn ${EXECUTE_ROLE_ARN} --role-session-name test | jq -r '.Credentials | "export AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport AWS_SESSION_TOKEN=\(.SessionToken)\n"')
      - env
      - export SEMVER=$(docker run --rm -v $BITBUCKET_CLONE_DIR:/repo gittools/gitversion:5.12.0-alpine.3.14-6.0 /repo -output json -showvariable FullSemVer)
      - echo ${IMAGE_NAME}:${SEMVER}
      - apt-get update && apt-get install -y maven
      - mvn -version
      - echo " Decodificando certificados .pem"
      - mkdir -p ./${APP_FOLDER}/ssl
      - echo "$CA_CERT_B64"     | base64 -d > ./${APP_FOLDER}/ssl/ca-cert.pem
      - echo "$CLIENT_CERT_B64" | base64 -d > ./${APP_FOLDER}/ssl/client-cert.pem
      - echo "$CLIENT_KEY_B64"  | base64 -d > ./${APP_FOLDER}/ssl/client-key.pem
      - chmod 600 ./${APP_FOLDER}/ssl/client-key.pem
      - envsubst < ${APP_FOLDER}/src/main/resources/application.properties > ${APP_FOLDER}/src/main/resources/application.properties.tmp
      - mv ${APP_FOLDER}/src/main/resources/application.properties.tmp ${APP_FOLDER}/src/main/resources/application.properties
      - ls -la ./${APP_FOLDER}/ssl/
      - cd common && mvn clean install -DskipTests
      - cd ../security && mvn clean install -DskipTests
      - cd ../${APP_FOLDER} && mvn package -DskipTests
      - docker build -t ${IMAGE_NAME}:${SEMVER} .
      - pipe: atlassian/aws-ecr-push-image:2.5.0
        variables:
          AWS_DEFAULT_REGION: ${ECR_REGION}
          AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
          AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
          AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
          IMAGE_NAME: ${IMAGE_NAME}
          TAGS: $SEMVER
          DEBUG: "true"
          
    services:
      - docker
    caches:
      - docker
  hadolint: &hadolint
    step: 
      name: Scanning the Dockerfile (HadoLint)
      image: hadolint/hadolint:latest-debian
      script:
        - hadolint Dockerfile
  update_ecs_service: &update_ecs_service
    name: update ecs task
    image: atlassian/default-image:4
    oidc: true
    script:
      - test -f dotenv && source dotenv
      - bash scripts/update-task.sh
  
  # Generador de variables para build
  generate_build_vars: &generate_build_vars
    step:
      name: generate variables
      artifacts:
        - dotenv
      script:
        - echo "export IMAGE_NAME=${API_NAME} APP_FOLDER=${API_FOLDER} ECR_REGION=us-east-1 AWS_ROLE_ARN=arn:aws:iam::227932815557:role/bitbucket-openid EXECUTE_ROLE_ARN=arn:aws:iam::555456986164:role/deployer" > dotenv
  
  # Generador de variables para update
  generate_update_vars: &generate_update_vars
    step:
      name: generate variables
      services:
        - docker
      caches:
        - docker
      script: 
        - export SEMVER=$(docker run --rm -v $BITBUCKET_CLONE_DIR:/repo gittools/gitversion:5.12.0-alpine.3.14-6.0 /repo -output json -showvariable FullSemVer)
        - echo "export IMAGE_TAG=${SEMVER}" > dotenv
        - cat scripts/envs/${installation}/${env}.env scripts/envs/${installation}/main.env >> dotenv
        - cp scripts/taskdef/${API_TASKDEF} task-template.yml
        - cp scripts/clusters/${env}-${installation}-ecs-cluster.yml cluster-config.yml
      artifacts:
        - dotenv
        - cluster-config.yml
        - task-template.yml
  
  # Build & Package App con manejo de certificados
  build_package_app: &build_package_app
    step:
      name: Build & Package App
      image: atlassian/default-image:2
      script:
        - test -f dotenv && source dotenv
        - apt-get update && apt-get install -y gettext
        - echo " Decodificando certificados .pem"
        - mkdir -p ./${API_FOLDER}/ssl
        - echo "$CA_CERT_B64"     | base64 -d > ./${API_FOLDER}/ssl/ca-cert.pem
        - echo "$CLIENT_CERT_B64" | base64 -d > ./${API_FOLDER}/ssl/client-cert.pem
        - echo "$CLIENT_KEY_B64"  | base64 -d > ./${API_FOLDER}/ssl/client-key.pem
        - chmod 600 ./${API_FOLDER}/ssl/client-key.pem
        - envsubst < ${API_FOLDER}/src/main/resources/application.properties > ${API_FOLDER}/src/main/resources/application.properties.tmp
        - mv ${API_FOLDER}/src/main/resources/application.properties.tmp ${API_FOLDER}/src/main/resources/application.properties
        - ls -la ./${API_FOLDER}/ssl/
        - apt-get update && apt-get install -y maven
        - mvn -version
        - cd common && mvn clean install -DskipTests
        - cd ../security && mvn clean install -DskipTests
        - cd ../${API_FOLDER} && mvn package -DskipTests
      services:
        - docker
      caches:
        - maven

pipelines:
  custom:
    # BUILD PIPELINES
    build_account_api:
      - variables:
          - name: API_NAME
            default: "enterprise-account-api"
          - name: API_FOLDER
            default: "account"
      - <<: *generate_build_vars
      - step: *build_push
    
    build_general_api:
      - variables:
          - name: API_NAME
            default: "enterprise-general-api"
          - name: API_FOLDER
            default: "general"
      - <<: *generate_build_vars
      - step: *build_push
    
    build_webservice_api:
      - variables:
          - name: API_NAME
            default: "enterprise-webservice-api"
          - name: API_FOLDER
            default: "webservice"
      - <<: *generate_build_vars
      - step: *build_push
    
    build_report_api:
      - variables:
          - name: API_NAME
            default: "enterprise-report-api"
          - name: API_FOLDER
            default: "report"
      - <<: *generate_build_vars
      - step: *build_push
    
    build_blacklist_api:
      - variables:
          - name: API_NAME
            default: "enterprise-blacklist-api"
          - name: API_FOLDER
            default: "blacklist"
      - <<: *generate_build_vars
      - step: *build_push
    
    # UPDATE PIPELINES
    update_account-api:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "account"
        - name: API_TASKDEF
          default: "enterprise-account-api.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app
    
    update_general-api:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "general"
        - name: API_TASKDEF
          default: "enterprisegeneral-api.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app
    
    update_webservice-api:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "webservice"
        - name: API_TASKDEF
          default: "enterprise-webservice-api.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app
    
    update_report-api:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "report"
        - name: API_TASKDEF
          default: "enterprise-report-api.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app
    
    update_blacklist-api:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "blacklist"
        - name: API_TASKDEF
          default: "enterprise-blacklist-api.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app
