# Template docker-push
# This template allows you to build and push your docker image to a Docker Hub account.
# The workflow allows running tests, code linting and security scans on feature branches (as well as master).
# The docker image will be validated and pushed to the docker registry after the code is merged to master.
# Prerequisites: $DOCKERHUB_USERNAME, $DOCKERHUB_PASSWORD setup as deployment variables

image: atlassian/default-image:2
clone:
  depth: full
definitions:
  build: &build_push
    name: build image
    oidc: true
    script:
      - export AWS_WEB_IDENTITY_TOKEN_FILE="/tmp/aws-web-identity-token-file"
      - echo ${BITBUCKET_STEP_OIDC_TOKEN} > ${AWS_WEB_IDENTITY_TOKEN_FILE}
      - test -f dotenv && source dotenv
      - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      - unzip awscliv2.zip
      - ./aws/install
      - eval $(aws sts assume-role --role-arn ${EXECUTE_ROLE_ARN} --role-session-name test | jq -r '.Credentials | "export AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport AWS_SESSION_TOKEN=\(.SessionToken)\n"')
      - env
      - export SEMVER=$(docker run --rm -v $BITBUCKET_CLONE_DIR:/repo gittools/gitversion:5.12.0-alpine.3.14-6.0 /repo -output json -showvariable FullSemVer)
      - echo ${IMAGE_NAME}:${SEMVER}
      - apt-get update && apt-get install -y build-essential clang make curl libcurl4-openssl-dev libmysqlclient-dev
      - clang --version
      - echo " Compilando servicios C"
      - echo " Compilando ${APP_FOLDER}"
      - (cd transporte_enterprise/TRANSPORT_MASTER_SOURCE/${APP_FOLDER} && make clean all)
      - echo "Listando binarios compilados en ${APP_FOLDER}:"
      - ls -la transporte_enterprise/TRANSPORT_MASTER_SOURCE/${APP_FOLDER}/
      - docker build -f Dockerfile.${APP_FOLDER} -t ${IMAGE_NAME}:${SEMVER} .
      - pipe: atlassian/aws-ecr-push-image:2.5.0
        variables:
          AWS_DEFAULT_REGION: ${ECR_REGION}
          AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
          AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
          AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN}
          IMAGE_NAME: ${IMAGE_NAME}
          TAGS: $SEMVER
          DEBUG: "true"
          
    services:
      - docker
    caches:
      - docker
  hadolint: &hadolint
    step: 
      name: Scanning the Dockerfile (HadoLint)
      image: hadolint/hadolint:latest-debian
      script:
        - hadolint Dockerfile
  update_ecs_service: &update_ecs_service
    name: update ecs task
    image: atlassian/default-image:4
    oidc: true
    script:
      - test -f dotenv && source dotenv
      - bash scripts/update-task.sh
  
  # Generador de variables para build
  generate_build_vars: &generate_build_vars
    step:
      name: generate variables
      artifacts:
        - dotenv
      script:
        - echo "export IMAGE_NAME=${API_NAME} APP_FOLDER=${API_FOLDER} ECR_REGION=us-east-1 AWS_ROLE_ARN=arn:aws:iam::227932815557:role/bitbucket-openid EXECUTE_ROLE_ARN=arn:aws:iam::555456986164:role/deployer" > dotenv
  
  # Generador de variables para update
  generate_update_vars: &generate_update_vars
    step:
      name: generate variables
      services:
        - docker
      caches:
        - docker
      script: 
        - export SEMVER=$(docker run --rm -v $BITBUCKET_CLONE_DIR:/repo gittools/gitversion:5.12.0-alpine.3.14-6.0 /repo -output json -showvariable FullSemVer)
        - echo "export IMAGE_TAG=${SEMVER}" > dotenv
        - cat scripts/envs/${installation}/${env}.env scripts/envs/${installation}/main.env >> dotenv
        - cp scripts/taskdef/${API_TASKDEF} task-template.yml
        - cp scripts/clusters/${env}-${installation}-ecs-cluster.yml cluster-config.yml
      artifacts:
        - dotenv
        - cluster-config.yml
        - task-template.yml
  
  # Build & Package App
  build_package_app: &build_package_app
    step:
      name: Build & Package App
      image: atlassian/default-image:2
      script:
        - test -f dotenv && source dotenv
        - apt-get update && apt-get install -y gettext
        - apt-get update && apt-get install -y build-essential clang make curl libcurl4-openssl-dev libmysqlclient-dev
        - clang --version
        - echo " Compilando servicios C"
        - echo " Compilando ${API_FOLDER}"
        - (cd transporte_enterprise/TRANSPORT_MASTER_SOURCE/${API_FOLDER} && make clean all)
      services:
        - docker
      caches:
        - docker

pipelines:
  custom:
    # BUILD PIPELINES
    build_dispatch_ack:
      - variables:
          - name: API_NAME
            default: "gkv-dispatch-ack"
          - name: API_FOLDER
            default: "DispatchACK"
      - <<: *generate_build_vars
      - step: *build_push

    build_dispatch_gkv:
      - variables:
          - name: API_NAME
            default: "gkv-dispatch-gkv"
          - name: API_FOLDER
            default: "DispatchGKV"
      - <<: *generate_build_vars
      - step: *build_push

    build_dispatch_mo:
      - variables:
          - name: API_NAME
            default: "gkv-dispatch-mo"
          - name: API_FOLDER
            default: "DispatchMO"
      - <<: *generate_build_vars
      - step: *build_push

    build_listen_gkv:
      - variables:
          - name: API_NAME
            default: "transporte-gkv-listengkv"
          - name: API_FOLDER
            default: "ListenGKV"
      - <<: *generate_build_vars
      - step: *build_push

    build_maintenance:
      - variables:
          - name: API_NAME
            default: "gkv-maintenance"
          - name: API_FOLDER
            default: "Maintenance"
      - <<: *generate_build_vars
      - step: *build_push
    
    # UPDATE PIPELINES
    update_dispatch-ack:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "DispatchACK"
        - name: API_TASKDEF
          default: "gkv-dispatch-ack.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app

    update_dispatch-gkv:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "DispatchGKV"
        - name: API_TASKDEF
          default: "gkv-dispatch-gkv.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app

    update_dispatch-mo:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "DispatchMO"
        - name: API_TASKDEF
          default: "gkv-dispatch-mo.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app

    update_listen-gkv:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "ListenGKV"
        - name: API_TASKDEF
          default: "transporte-gkv-listengkv.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app

    update_maintenance:
      - variables:
        - name: installation
          default: "smscorp"
          allowed-values:
            - smscorp
        - name: env
          default: dev
          allowed-values:
            - dev
            - prd
        - name: API_FOLDER
          default: "Maintenance"
        - name: API_TASKDEF
          default: "gkv-maintenance.yml"
      - <<: *generate_update_vars
      - step: *update_ecs_service
      - <<: *build_package_app
