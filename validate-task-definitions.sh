#!/bin/bash

# Script para validar las task definitions de los servicios GKV

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"
}

# Archivos de task definition a validar
TASK_DEFINITIONS=(
    "scripts/taskdef/gkv-dispatch-ack.yml"
    "scripts/taskdef/transporte-gkv-dispatchgkv.yml"
    "scripts/taskdef/transporte-gkv-dispatchmo.yml"
    "scripts/taskdef/transporte-gkv-listengkv.yml"
    "scripts/taskdef/transporte-gkv-maintenance.yml"
)

# Función para validar un archivo YAML
validate_yaml() {
    local file=$1
    
    if [ ! -f "$file" ]; then
        error "Archivo no encontrado: $file"
        return 1
    fi
    
    # Verificar sintaxis YAML básica
    if command -v python3 &> /dev/null; then
        if ! python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
            error "Sintaxis YAML inválida en: $file"
            return 1
        fi
    fi
    
    return 0
}

# Función para validar configuración de health check
validate_health_check() {
    local file=$1
    local service_name=$(basename "$file" .yml)
    
    info "Validando health check en: $service_name"
    
    # Verificar que existe la configuración de health check
    if ! grep -q "healthCheck:" "$file"; then
        error "Health check no configurado en: $file"
        return 1
    fi
    
    # Verificar comando de health check
    if ! grep -q "curl -f http://localhost/health" "$file"; then
        error "Comando de health check incorrecto en: $file"
        return 1
    fi
    
    # Verificar configuración de puertos
    if ! grep -q "containerPort: 80" "$file"; then
        error "Puerto 80 (nginx) no configurado en: $file"
        return 1
    fi
    
    # Verificar puerto específico del servicio
    local expected_ports=(3738 3739 3740 3741 3742)
    local found_service_port=false
    
    for port in "${expected_ports[@]}"; do
        if grep -q "containerPort: $port" "$file"; then
            found_service_port=true
            info "✓ Puerto del servicio encontrado: $port"
            break
        fi
    done
    
    if [ "$found_service_port" = false ]; then
        error "Puerto del servicio no configurado en: $file"
        return 1
    fi
    
    log "✓ Health check válido en: $service_name"
    return 0
}

# Función para validar variables de imagen
validate_image_variables() {
    local file=$1
    local service_name=$(basename "$file" .yml)
    
    info "Validando variables de imagen en: $service_name"
    
    # Mapeo de archivos a variables esperadas
    case "$service_name" in
        "gkv-dispatch-ack")
            expected_var="DISPATCHACK_IMG"
            ;;
        "transporte-gkv-dispatchgkv")
            expected_var="DISPATCHGKV_IMG"
            ;;
        "transporte-gkv-dispatchmo")
            expected_var="DISPATCHMO_IMG"
            ;;
        "transporte-gkv-listengkv")
            expected_var="LISTENGKV_IMG"
            ;;
        "transporte-gkv-maintenance")
            expected_var="GKVMAINTENANCE_IMG"
            ;;
        *)
            error "Servicio desconocido: $service_name"
            return 1
            ;;
    esac
    
    if ! grep -q "\${${expected_var}}" "$file"; then
        error "Variable de imagen incorrecta en: $file (esperada: $expected_var)"
        return 1
    fi
    
    log "✓ Variable de imagen válida: $expected_var"
    return 0
}

# Función para validar configuración de logs
validate_log_configuration() {
    local file=$1
    local service_name=$(basename "$file" .yml)
    
    info "Validando configuración de logs en: $service_name"
    
    # Verificar que existe configuración de logs
    if ! grep -q "logConfiguration:" "$file"; then
        error "Configuración de logs no encontrada en: $file"
        return 1
    fi
    
    # Verificar driver de logs
    if ! grep -q "logDriver.*awslogs" "$file"; then
        error "Driver de logs incorrecto en: $file"
        return 1
    fi
    
    # Verificar región
    if ! grep -q "awslogs-region.*us-east-1" "$file"; then
        error "Región de logs incorrecta en: $file"
        return 1
    fi
    
    log "✓ Configuración de logs válida"
    return 0
}

# Función principal de validación
validate_task_definition() {
    local file=$1
    local service_name=$(basename "$file" .yml)
    
    log "=== Validando: $service_name ==="
    
    local validation_passed=true
    
    # Validar sintaxis YAML
    if ! validate_yaml "$file"; then
        validation_passed=false
    fi
    
    # Validar health check
    if ! validate_health_check "$file"; then
        validation_passed=false
    fi
    
    # Validar variables de imagen
    if ! validate_image_variables "$file"; then
        validation_passed=false
    fi
    
    # Validar configuración de logs
    if ! validate_log_configuration "$file"; then
        validation_passed=false
    fi
    
    if [ "$validation_passed" = true ]; then
        log "✅ $service_name: VÁLIDO"
        return 0
    else
        error "❌ $service_name: INVÁLIDO"
        return 1
    fi
}

# Función principal
main() {
    log "=== Iniciando validación de task definitions ==="
    
    local success_count=0
    local total_count=${#TASK_DEFINITIONS[@]}
    
    for task_def in "${TASK_DEFINITIONS[@]}"; do
        if validate_task_definition "$task_def"; then
            ((success_count++))
        fi
        echo ""
    done
    
    log "=== Resumen de validación ==="
    log "Task definitions válidas: ${success_count}/${total_count}"
    
    if [ $success_count -eq $total_count ]; then
        log "🎉 Todas las task definitions son válidas"
        return 0
    else
        error "❌ Algunas task definitions tienen errores"
        return 1
    fi
}

# Mostrar ayuda
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Uso: $0"
    echo ""
    echo "Valida las task definitions de los servicios GKV"
    echo ""
    echo "Verifica:"
    echo "  - Sintaxis YAML válida"
    echo "  - Configuración de health checks"
    echo "  - Variables de imagen correctas"
    echo "  - Configuración de logs"
    echo "  - Port mappings"
    exit 0
fi

# Ejecutar función principal
main
