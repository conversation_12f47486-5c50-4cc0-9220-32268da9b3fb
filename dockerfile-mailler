FROM openjdk:8-jdk-slim

# Crear usuario y estructura de carpetas
RUN useradd -m -d /home/<USER>/bin/bash appmobid && \
    mkdir -p /home/<USER>/app && \
    mkdir -p /home/<USER>/ssl && \
    mkdir -p /home/<USER>/properties-docker/wsmailer/properties/1.0.0 && \
    mkdir -p /home/<USER>/properties-docker/ws-enterprise-cl/properties/1.0.0 && \
    chown -R appmobid:appmobid /home/<USER>

# Copiar certificados generados en el pipeline
COPY ./ssl/ /home/<USER>/ssl/
RUN chown -R appmobid:appmobid /home/<USER>/ssl && \
    chmod 644 /home/<USER>/ssl/ca-cert.pem && \
    chmod 644 /home/<USER>/ssl/client-cert.pem && \
    chmod 600 /home/<USER>/ssl/client-key.pem

# Verificación de certificados SSL
RUN echo "=== Contenido de la carpeta SSL ===" && \
    ls -la /home/<USER>/ssl/ && \
    openssl x509 -in /home/<USER>/ssl/ca-cert.pem -text -noout && \
    openssl verify -CAfile /home/<USER>/ssl/ca-cert.pem /home/<USER>/ssl/client-cert.pem

# Verificación de archivos de configuración
RUN echo "=== Verificación archivos de configuración ===" && \
    ls -la /home/<USER>/properties-docker/ws-enterprise-cl/properties/1.0.0/

# Copiar WAR
COPY ./wsmailer.war /home/<USER>/app/wsmailer.war

# Copiar archivos de propiedades
COPY ./properties/application.properties /home/<USER>/properties-docker/wsmailer/properties/1.0.0/
COPY ./properties/config.properties      /home/<USER>/properties-docker/wsmailer/properties/
COPY ./properties/log4j2.properties      /home/<USER>/properties-docker/wsmailer/properties/1.0.0/
COPY ./properties/msg.properties         /home/<USER>/properties-docker/wsmailer/properties/

# Ajustar permisos de configuración
RUN chown -R appmobid:appmobid /home/<USER>
    chmod -R 644 /home/<USER>/properties-docker/wsmailer/properties/1.0.0/

# Cambiar a root para instalar NGINX
USER root

# Instalar NGINX y herramientas mínimas
RUN apt-get update && apt-get install -y nginx curl

# Copiar configuración de NGINX
COPY nginx.config /etc/nginx/sites-available/default

# Crear archivo estático para health check
RUN echo "OK" > /var/www/html/health

# Copiar script de arranque
COPY start.sh /start.sh
RUN chmod +x /start.sh

# Cambiar al usuario no root
USER appmobid

# Exponer puertos de la app y de nginx
EXPOSE 8080 80

USER root
# Ejecutar aplicación y nginx
CMD ["/start.sh"]
