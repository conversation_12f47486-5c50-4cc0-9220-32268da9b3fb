# Health Checks para Servicios GKV Transport

## Resumen

Este documento describe la implementación de health checks para los servicios de transporte GKV en AWS ECS, siguiendo el patrón del proyecto mailer con nginx como proxy reverso.

## Arquitectura

### Componentes
1. **Servicio C**: El servicio principal corriendo en puerto específico (3738-3742)
2. **Nginx**: Proxy reverso en puerto 80 con endpoint de health check
3. **Health Check**: Endpoint `/health` que verifica el estado del servicio

### Flujo de Health Check
```
AWS ELB/ECS → nginx:80/health → Archivo estático + Verificación del servicio C
```

## Archivos Creados

### 1. Configuración de Nginx (`nginx-gkv.config`)
- Proxy reverso hacia el servicio C
- Endpoint `/health` para health checks
- Endpoint `/service-health` para verificar el servicio directamente

### 2. Script de Inicio (`start-gkv.sh`)
- Inicia el servicio C en background
- Inicia nginx en foreground
- Monitorea ambos procesos
- Maneja señales de terminación correctamente

### 3. Dockerfiles Específicos
- `Dockerfile.DispatchACK`
- `Dockerfile.DispatchGKV`
- `Dockerfile.DispatchMO`
- `Dockerfile.ListenGKV`
- `Dockerfile.Maintenance`

### 4. Scripts de Utilidad
- `generate-dockerfiles.sh`: Genera Dockerfiles para todos los servicios
- `update-ecs-health-checks.sh`: Actualiza task definitions con health checks

## Configuración por Servicio

| Servicio | Puerto Interno | Binario | Health Check Port |
|----------|---------------|---------|-------------------|
| DispatchACK | 3738 | dispatchACK | 80 |
| DispatchGKV | 3739 | dispatchGKV | 80 |
| DispatchMO | 3740 | dispatchMO | 80 |
| ListenGKV | 3741 | listenGKV | 80 |
| Maintenance | 3742 | maintenanceSRV | 80 |

## Health Check Endpoints

### `/health`
- **Método**: GET
- **Puerto**: 80
- **Respuesta**: Archivo estático con información del servicio
- **Uso**: Health check básico de AWS ELB/ECS

### `/service-health`
- **Método**: GET
- **Puerto**: 80
- **Respuesta**: Proxy al endpoint de health del servicio C
- **Uso**: Verificación directa del servicio

## Configuración de AWS ECS

### Health Check Container
```json
{
  "healthCheck": {
    "command": [
      "CMD-SHELL",
      "curl -f http://localhost/health || exit 1"
    ],
    "interval": 30,
    "timeout": 10,
    "retries": 3,
    "startPeriod": 60
  }
}
```

### Target Group Health Check
```json
{
  "healthCheckPath": "/health",
  "healthCheckProtocol": "HTTP",
  "healthCheckPort": "80",
  "healthCheckIntervalSeconds": 30,
  "healthCheckTimeoutSeconds": 10,
  "healthyThresholdCount": 2,
  "unhealthyThresholdCount": 3
}
```

## Uso

### 1. Construir Imágenes
```bash
# Generar todos los Dockerfiles
./generate-dockerfiles.sh

# Construir imagen específica
docker build -f Dockerfile.DispatchACK -t gkv-dispatch-ack .
```

### 2. Ejecutar Localmente
```bash
# Ejecutar con health check
docker run -p 80:80 -p 3738:3738 gkv-dispatch-ack

# Verificar health check
curl http://localhost/health
```

### 3. Actualizar ECS Task Definitions
```bash
# Actualizar todas las task definitions
./update-ecs-health-checks.sh

# Modo dry-run para verificar cambios
./update-ecs-health-checks.sh --dry-run
```

### 4. Pipeline de Bitbucket
El pipeline ahora usa los Dockerfiles específicos:
```yaml
- docker build -f Dockerfile.${APP_FOLDER} -t ${IMAGE_NAME}:${SEMVER} .
```

## Monitoreo

### Logs del Contenedor
```bash
# Ver logs del servicio
docker logs <container_id>

# Logs específicos de nginx
docker exec <container_id> tail -f /var/log/nginx/access.log
```

### Verificación Manual
```bash
# Health check básico
curl http://localhost/health

# Health check del servicio
curl http://localhost/service-health

# Estado de procesos
docker exec <container_id> ps aux
```

## Troubleshooting

### Problemas Comunes

1. **Health check falla**
   - Verificar que nginx esté corriendo
   - Verificar que el archivo `/var/www/html/health` existe
   - Verificar logs de nginx

2. **Servicio C no responde**
   - Verificar que el binario existe y tiene permisos
   - Verificar que el puerto está libre
   - Verificar logs del servicio

3. **Proxy no funciona**
   - Verificar configuración de nginx
   - Verificar que el servicio C está escuchando en el puerto correcto

### Comandos de Diagnóstico
```bash
# Verificar puertos abiertos
netstat -tuln

# Verificar procesos
ps aux | grep -E "(nginx|dispatch|listen|maintenance)"

# Verificar configuración de nginx
nginx -t

# Logs de nginx
tail -f /var/log/nginx/error.log
```

## Próximos Pasos

1. **Probar health checks** en entorno de desarrollo
2. **Actualizar task definitions** de ECS
3. **Configurar alertas** de CloudWatch para health checks
4. **Documentar métricas** específicas por servicio
5. **Implementar health checks avanzados** que verifiquen funcionalidad específica
