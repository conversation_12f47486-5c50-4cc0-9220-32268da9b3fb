# Actualización de Task Definitions con Health Checks

## Resumen

Se han actualizado todas las task definitions de los servicios GKV para incluir health checks siguiendo el patrón del proyecto mailer, con nginx como proxy reverso y endpoints de health check.

## Archivos Actualizados

### Task Definitions
1. `scripts/taskdef/gkv-dispatch-ack.yml`
2. `scripts/taskdef/transporte-gkv-dispatchgkv.yml`
3. `scripts/taskdef/transporte-gkv-dispatchmo.yml`
4. `scripts/taskdef/transporte-gkv-listengkv.yml`
5. `scripts/taskdef/transporte-gkv-maintenance.yml`

### Variables de Imagen (main.env)
- `DISPATCHACK_IMG`: transporte-gkv-dispatchack
- `DISPATCHGKV_IMG`: transporte-gkv-dispatchgkv
- `DISPATCHMO_IMG`: transporte-gkv-dispatchmo
- `LISTENGKV_IMG`: transporte-gkv-listengkv
- `GKVMAINTENANCE_IMG`: transporte-gkv-maintenance

## Configuración Implementada

### Port Mappings
Cada servicio expone dos puertos:
- **Puerto 80**: Nginx (para ALB/ELB health checks)
- **Puerto específico**: Servicio C (proxy desde nginx)

| Servicio | Puerto Nginx | Puerto Servicio |
|----------|--------------|-----------------|
| DispatchACK | 80 | 3738 |
| DispatchGKV | 80 | 3739 |
| DispatchMO | 80 | 3740 |
| ListenGKV | 80 | 3741 |
| Maintenance | 80 | 3742 |

### Health Check Configuration
```yaml
healthCheck:
  command: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
  interval: 30
  timeout: 5
  retries: 3
  startPeriod: 60
```

### Log Configuration
```yaml
logConfiguration:
  logDriver: "awslogs"
  options:
    "awslogs-group": "transporte-gkv-[servicio]"
    "awslogs-region": "us-east-1"
    "awslogs-stream-prefix": "transporte-gkv-[servicio]"
```

## Ejemplo de Configuración Completa

```yaml
config:
  service: transporte-gkv-dispatchack
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
    containerDefinitions:
      - name: app
        image: ${DISPATCHACK_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-dispatchack"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-dispatchack"
        portMappings:
          - containerPort: 80   # NGINX escucha aquí (para el ALB)
          - containerPort: 3738 # Servicio DispatchACK escucha aquí (NGINX hace proxy)
        healthCheck:
          command: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
          interval: 30
          timeout: 5
          retries: 3
          startPeriod: 60
```

## Beneficios de la Implementación

### 1. Health Checks Robustos
- **Endpoint estático**: `/health` siempre disponible
- **Verificación del servicio**: Nginx verifica que el servicio C responda
- **Compatibilidad AWS**: Funciona con ECS y ELB health checks

### 2. Arquitectura Resiliente
- **Proxy reverso**: Nginx maneja las conexiones externas
- **Aislamiento**: El servicio C está protegido detrás del proxy
- **Monitoreo**: Logs centralizados en CloudWatch

### 3. Escalabilidad
- **Load balancing**: ALB puede distribuir tráfico eficientemente
- **Health checks**: Servicios no saludables son removidos automáticamente
- **Rolling deployments**: Deployments sin downtime

## Validación

### Script de Validación
Se creó `validate-task-definitions.sh` que verifica:
- ✅ Sintaxis YAML válida
- ✅ Configuración de health checks
- ✅ Variables de imagen correctas
- ✅ Port mappings apropiados
- ✅ Configuración de logs

### Verificación Manual
```bash
# Validar todas las task definitions
./validate-task-definitions.sh

# Verificar sintaxis YAML específica
python3 -c "import yaml; print(yaml.safe_load(open('scripts/taskdef/gkv-dispatch-ack.yml')))"
```

## Integración con Pipeline

### Bitbucket Pipeline
El pipeline ya está configurado para:
1. Compilar servicios C
2. Construir imágenes Docker con health checks
3. Usar task definitions actualizadas
4. Desplegar a ECS con health checks habilitados

### Variables de Entorno
Las variables en `main.env` están alineadas con:
- Nombres de imagen en ECR
- Variables usadas en task definitions
- Configuración del pipeline

## Próximos Pasos

### 1. Despliegue
```bash
# Ejecutar pipeline de build
# Bitbucket → Custom → build_dispatch_ack

# Ejecutar pipeline de update
# Bitbucket → Custom → update_dispatch-ack
```

### 2. Verificación en AWS
- Verificar que las task definitions se registren correctamente
- Confirmar que los health checks funcionen en ECS
- Validar que el ALB detecte servicios saludables

### 3. Monitoreo
- Configurar alertas de CloudWatch para health checks
- Monitorear logs de nginx y servicios C
- Establecer métricas de disponibilidad

## Troubleshooting

### Health Check Falla
1. Verificar que nginx esté corriendo
2. Verificar que el archivo `/var/www/html/health` existe
3. Verificar logs del contenedor

### Servicio No Responde
1. Verificar que el binario C esté corriendo
2. Verificar que el puerto esté libre
3. Verificar configuración de nginx

### Task Definition No Se Registra
1. Verificar sintaxis YAML
2. Verificar que las variables de imagen estén definidas
3. Verificar permisos de ECS

## Conclusión

La implementación de health checks está completa y sigue las mejores prácticas:
- ✅ Patrón probado del proyecto mailer
- ✅ Configuración consistente entre servicios
- ✅ Integración completa con AWS ECS/ELB
- ✅ Scripts de validación y testing
- ✅ Documentación completa

Los servicios GKV ahora tienen health checks robustos que permitirán deployments más confiables y mejor monitoreo en AWS.
