#!/bin/bash

# Script para generar Dockerfiles específicos para cada servicio GKV con health checks

# Definir servicios y sus binarios
declare -A SERVICES=(
    ["DispatchACK"]="dispatchACK"
    ["DispatchGKV"]="dispatchGKV"
    ["DispatchMO"]="dispatchMO"
    ["ListenGKV"]="listenGKV"
    ["Maintenance"]="maintenanceSRV"
)

# Definir puertos por servicio (si son diferentes)
declare -A PORTS=(
    ["DispatchACK"]="3738"
    ["DispatchGKV"]="3739"
    ["DispatchMO"]="3740"
    ["ListenGKV"]="3741"
    ["Maintenance"]="3742"
)

# Función para generar Dockerfile
generate_dockerfile() {
    local service_name=$1
    local binary_name=$2
    local port=$3
    local dockerfile_name="Dockerfile.${service_name}"
    
    echo "Generando ${dockerfile_name}..."
    
    cat > "${dockerfile_name}" << EOF
# Dockerfile específico para ${service_name} con health check
FROM alpine:3.18 AS builder

LABEL description="Builder container - ${service_name}"

RUN apk update && apk add --no-cache \\ 
    autoconf binutils build-base clang curl curl-dev libtool libxslt-dev \\ 
    linux-headers lz4-dev make musl-dev mysql-dev zlib-dev zstd-dev

RUN mkdir -p /Desa/${service_name} && mkdir -p /Desa/Common 
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Common /Desa/Common
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/${service_name} /Desa/${service_name}
COPY ./transporte_enterprise/TRANSPORT_MASTER_SOURCE/Test/conf/params.cfg  /Desa/${service_name}/ 

RUN cd /Desa/${service_name} && make clean all

############################
# STEP 2: Build runtime image
############################
FROM alpine:3.18

LABEL description="Run container - ${service_name} with health check"

RUN apk update && apk add --no-cache \\
    bash curl libcurl mariadb-connector-c net-tools nginx tzdata && \\
    mkdir -p /home/<USER>/GKV/bin \\
             /home/<USER>/GKV/cfg \\
             /home/<USER>/GKV/run \\
             /home/<USER>/GKV/conf \\
             /home/<USER>/GKV/log \\
             /var/www/html && \\
    addgroup -S apptiaxa && \\
    adduser -S apptiaxa -G apptiaxa && \\
    chown -R apptiaxa:apptiaxa /home/<USER>/GKV/

ENV TZ=America/Santiago
ENV SERVICE_NAME=${service_name}
ENV SERVICE_PORT=${port}
ENV SERVICE_BINARY=${binary_name}

# Copy the compiled binary
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/${service_name}/${binary_name} /home/<USER>/GKV/bin/
COPY --from=builder --chown=apptiaxa:apptiaxa /Desa/${service_name}/params.cfg /home/<USER>/GKV/conf/

# Copy nginx configuration
COPY nginx-gkv.config /etc/nginx/http.d/default.conf

# Create initial health check file
RUN echo "OK" > /var/www/html/health && \\
    echo "Service: ${service_name}" >> /var/www/html/health

# Copy startup script
COPY start-gkv.sh /start-gkv.sh
RUN chmod +x /start-gkv.sh

# Expose both service port and nginx port
EXPOSE ${port} 80

WORKDIR /home/<USER>/GKV/bin

# Health check for AWS ECS/ELB
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \\
    CMD curl -f http://localhost/health || exit 1

# Use the startup script as entrypoint
CMD ["/start-gkv.sh"]
EOF

    echo "✓ ${dockerfile_name} generado"
}

# Generar Dockerfiles para todos los servicios
echo "=== Generando Dockerfiles para servicios GKV ==="

for service in "${!SERVICES[@]}"; do
    binary="${SERVICES[$service]}"
    port="${PORTS[$service]}"
    generate_dockerfile "$service" "$binary" "$port"
done

echo ""
echo "=== Dockerfiles generados ==="
ls -la Dockerfile.*

echo ""
echo "=== Para construir las imágenes, usa: ==="
for service in "${!SERVICES[@]}"; do
    echo "docker build -f Dockerfile.${service} -t gkv-${service,,} ."
done
