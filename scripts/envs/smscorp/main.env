# enterprise-account docker image urls
export LISTENGKV_IMG=${DOCKER_BASE_URL}/transporte-gkv-listengkv:${IMAGE_TAG:-dev}

# enterprise-general docker image urls
export DISPATCHACK_IMG=${DOCKER_BASE_URL}/transporte-gkv-dispatchack:${IMAGE_TAG:-dev}

# enterprise-blacklist docker image urls
export BLACKLISTAPI_IMG=${DOCKER_BASE_URL}/enterprise-blacklist-api:${IMAGE_TAG:-dev}

# enterprise-webservice docker image urls
export WEBSERVICEAPI_IMG=${DOCKER_BASE_URL}/enterprise-webservice-api:${IMAGE_TAG:-dev}

# enterprise-report docker image urls
export REPORTAPI_IMG=${DOCKER_BASE_URL}/enterprise-report-api:${IMAGE_TAG:-dev}

# generic / reusable engineering docker images
export TELEGRAF_IMG=************.dkr.ecr.us-east-2.amazonaws.com/telegraf:${TELEGRAF_TAG:-0.1.0-ci.946}

# AWS_REGION
export AWS_REGION=us-east-1
export AWS_DEFAULT_REGION=us-east-1
export AWS_MAIN_ACCOUNT_ID="************" ###MARK_MAIN_ACCOUNT
export AWS_OIDC_ROLE_ARN="arn:aws:iam::************:role/bitbucket-openid"
