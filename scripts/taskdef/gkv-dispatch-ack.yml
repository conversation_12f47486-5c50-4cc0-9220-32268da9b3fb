config:
  service: transporte-gkv-dispatchack
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: transporte-gkv-dispatchack
    containerDefinitions:
      - name: app
        image: ${DISPATCHACK_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-dispatchack"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-dispatchack"
    

