config:
  service: enterprise-general-api
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: enterprise-general-api
    containerDefinitions:
      - name: app
        image: ${GENERALAPI_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "enterprise-general-api"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "enterprise-general-api"
    

