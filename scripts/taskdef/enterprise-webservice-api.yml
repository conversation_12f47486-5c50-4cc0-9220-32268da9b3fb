config:
  service: enterprise-webservice-api
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: enterprise-webservice-api
    containerDefinitions:
      - name: app
        image: ${WEBSERVICEAPI_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "enterprise-webservice-api"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "enterprise-webservice-api"
    

