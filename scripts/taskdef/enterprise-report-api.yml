config:
  service: enterprise-report-api
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: enterprise-report-api
    containerDefinitions:
      - name: app
        image: ${REPORTAPI_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "enterprise-report-api"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "enterprise-report-api"
    

