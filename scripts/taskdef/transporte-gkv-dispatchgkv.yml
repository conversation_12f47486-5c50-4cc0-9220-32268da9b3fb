config:
  service: transporte-gkv-dispatchgkv
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: transporte-gkv-dispatchgkv
    containerDefinitions:
      - name: app
        image: ${DISPATCHGKV_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-dispatchgkv"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-dispatchgkv"
    

