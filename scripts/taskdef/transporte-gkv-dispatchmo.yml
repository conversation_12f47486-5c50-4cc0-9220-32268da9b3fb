config:
  service: transporte-gkv-dispatchmo
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
    containerDefinitions:
      - name: app
        image: ${DISPATCHMO_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-dispatchmo"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-dispatchmo"
        portMappings:
          - containerPort: 80   # NGINX escucha aquí (para el ALB)
          - containerPort: 3740 # Servicio DispatchMO escucha aquí (NGINX hace proxy)
        healthCheck:
          command: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
          interval: 30
          timeout: 5
          retries: 3
          startPeriod: 60
    

