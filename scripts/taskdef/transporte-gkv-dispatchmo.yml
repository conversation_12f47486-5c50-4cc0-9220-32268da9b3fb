config:
  service: transporte-gkv-dispatchmo
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: transporte-gkv-dispatchmo
    containerDefinitions:
      - name: app
        image: ${DISPATCHMO_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-dispatchmo"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-dispatchmo"
    

