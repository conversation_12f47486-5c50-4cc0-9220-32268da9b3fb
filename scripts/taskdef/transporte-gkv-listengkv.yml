config:
  service: transporte-gkv-listengkv
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: transporte-gkv-listengkv
    containerDefinitions:
      - name: app
        image: ${LISTENGKV_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-listengkv"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-listengkv"
    

