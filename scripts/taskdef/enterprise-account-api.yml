config:
  service: enterprise-account-api
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: enterprise-account-api
    containerDefinitions:
      - name: app
        image: ${ACCOUNTAPI_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "enterprise-account-api"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "enterprise-account-api"
    

