config:
  service: transporte-gkv-maintenance
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
    containerDefinitions:
      - name: app
        image: ${GKVMAINTENANCE_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-maintenance"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-maintenance"
        portMappings:
          - containerPort: 80   # NGINX escucha aquí (para el ALB)
          - containerPort: 3742 # Servicio Maintenance escucha aquí (NGINX hace proxy)
        healthCheck:
          command: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
          interval: 30
          timeout: 5
          retries: 3
          startPeriod: 60
    

