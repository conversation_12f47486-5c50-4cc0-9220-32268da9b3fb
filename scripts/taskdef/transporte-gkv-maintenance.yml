config:
  service: transporte-gkv-maintenance
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: transporte-gkv-maintenance
    containerDefinitions:
      - name: app
        image: ${GKVMAINTENANCE_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "transporte-gkv-maintenance"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "transporte-gkv-maintenance"
    

