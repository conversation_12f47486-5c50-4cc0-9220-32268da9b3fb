config:
  service: enterprise-blacklist-api
  service_config:
    propagateTags: "SERVICE"
    enableECSManagedTags: true
    desiredCount: 1
  task_config:
    cpu: "256"
    memory: "512"
   # volumes:
   #   - name: enterprise-blacklist-api
    containerDefinitions:
      - name: app
        image: ${BLACKLISTAPI_IMG}
        logConfiguration:
          logDriver: "awslogs"
          options:
            "awslogs-group": "enterprise-blacklist-api"
            "awslogs-region": "us-east-1"
            "awslogs-stream-prefix": "enterprise-blacklist-api"
    

