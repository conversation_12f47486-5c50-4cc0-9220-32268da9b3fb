#!/bin/bash

# Script para probar los health checks de los servicios GKV

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"
}

# Servicios a probar
SERVICES=("DispatchACK" "DispatchGKV" "DispatchMO" "ListenGKV" "Maintenance")

# Función para construir imagen
build_image() {
    local service=$1
    local image_name="gkv-${service,,}"
    
    info "Construyendo imagen para ${service}..."
    
    if docker build -f "Dockerfile.${service}" -t "${image_name}" . > /dev/null 2>&1; then
        log "✓ Imagen ${image_name} construida exitosamente"
        return 0
    else
        error "✗ Error al construir imagen ${image_name}"
        return 1
    fi
}

# Función para probar health check
test_health_check() {
    local service=$1
    local image_name="gkv-${service,,}"
    local container_name="test-${service,,}"
    local port_mapping=""
    
    # Definir mapeo de puertos según el servicio
    case $service in
        "DispatchACK") port_mapping="-p 8080:80 -p 3738:3738" ;;
        "DispatchGKV") port_mapping="-p 8081:80 -p 3739:3739" ;;
        "DispatchMO") port_mapping="-p 8082:80 -p 3740:3740" ;;
        "ListenGKV") port_mapping="-p 8083:80 -p 3741:3741" ;;
        "Maintenance") port_mapping="-p 8084:80 -p 3742:3742" ;;
    esac
    
    info "Probando health check para ${service}..."
    
    # Limpiar contenedor anterior si existe
    docker rm -f "${container_name}" > /dev/null 2>&1 || true
    
    # Ejecutar contenedor
    if ! docker run -d --name "${container_name}" ${port_mapping} "${image_name}" > /dev/null 2>&1; then
        error "✗ Error al ejecutar contenedor ${container_name}"
        return 1
    fi
    
    # Esperar que el contenedor inicie
    info "Esperando que el servicio ${service} inicie..."
    sleep 10
    
    # Obtener puerto de health check
    local health_port=$(echo $port_mapping | grep -o '\-p [0-9]*:80' | cut -d' ' -f2 | cut -d':' -f1)
    
    # Probar health check
    local max_attempts=6
    local attempt=1
    local success=false
    
    while [ $attempt -le $max_attempts ]; do
        info "Intento ${attempt}/${max_attempts}: Probando health check en puerto ${health_port}..."
        
        if curl -f -s "http://localhost:${health_port}/health" > /dev/null 2>&1; then
            log "✓ Health check exitoso para ${service}"
            success=true
            break
        fi
        
        sleep 5
        ((attempt++))
    done
    
    # Mostrar logs si falló
    if [ "$success" = false ]; then
        error "✗ Health check falló para ${service}"
        warn "Logs del contenedor:"
        docker logs "${container_name}" | tail -20
        warn "Estado del contenedor:"
        docker ps -a --filter "name=${container_name}"
    fi
    
    # Limpiar contenedor
    docker rm -f "${container_name}" > /dev/null 2>&1 || true
    
    [ "$success" = true ]
}

# Función para probar construcción de todas las imágenes
test_all_builds() {
    log "=== Probando construcción de todas las imágenes ==="
    
    local success_count=0
    local total_count=${#SERVICES[@]}
    
    for service in "${SERVICES[@]}"; do
        if build_image "$service"; then
            ((success_count++))
        fi
        echo ""
    done
    
    log "Imágenes construidas: ${success_count}/${total_count}"
    
    if [ $success_count -eq $total_count ]; then
        log "✓ Todas las imágenes se construyeron exitosamente"
        return 0
    else
        error "✗ Algunas imágenes fallaron al construirse"
        return 1
    fi
}

# Función para probar health checks de todas las imágenes
test_all_health_checks() {
    log "=== Probando health checks de todos los servicios ==="
    
    local success_count=0
    local total_count=${#SERVICES[@]}
    
    for service in "${SERVICES[@]}"; do
        if test_health_check "$service"; then
            ((success_count++))
        fi
        echo ""
    done
    
    log "Health checks exitosos: ${success_count}/${total_count}"
    
    if [ $success_count -eq $total_count ]; then
        log "✓ Todos los health checks funcionan correctamente"
        return 0
    else
        error "✗ Algunos health checks fallaron"
        return 1
    fi
}

# Función para limpiar recursos
cleanup() {
    info "Limpiando recursos de prueba..."
    
    # Detener y remover contenedores de prueba
    for service in "${SERVICES[@]}"; do
        container_name="test-${service,,}"
        docker rm -f "${container_name}" > /dev/null 2>&1 || true
    done
    
    log "✓ Limpieza completada"
}

# Función principal
main() {
    log "=== Iniciando pruebas de health checks para servicios GKV ==="
    
    # Verificar que Docker esté disponible
    if ! command -v docker &> /dev/null; then
        error "Docker no está instalado o no está disponible"
        exit 1
    fi
    
    # Verificar que curl esté disponible
    if ! command -v curl &> /dev/null; then
        error "curl no está instalado"
        exit 1
    fi
    
    # Configurar trap para limpieza
    trap cleanup EXIT
    
    # Ejecutar pruebas
    if [ "$1" = "--build-only" ]; then
        test_all_builds
    elif [ "$1" = "--health-only" ]; then
        test_all_health_checks
    else
        test_all_builds && test_all_health_checks
    fi
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log "🎉 Todas las pruebas pasaron exitosamente"
    else
        error "❌ Algunas pruebas fallaron"
    fi
    
    exit $exit_code
}

# Mostrar ayuda
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Uso: $0 [opciones]"
    echo ""
    echo "Prueba los health checks de los servicios GKV"
    echo ""
    echo "Opciones:"
    echo "  --build-only    Solo construir imágenes, no probar health checks"
    echo "  --health-only   Solo probar health checks (asume que las imágenes existen)"
    echo "  --help          Mostrar esta ayuda"
    echo ""
    echo "Sin opciones: Construir imágenes y probar health checks"
    exit 0
fi

# Ejecutar función principal
main "$@"
